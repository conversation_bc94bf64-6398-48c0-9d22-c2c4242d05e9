/*

This file contains the default configuration for the vessel logbook

*/

'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useConfiguredLazyQuery } from '@/hooks/useConfiguredLazyQuery'
import {
    AssetReporting_LogBookEntrySection,
    CrewMembers_LogBookEntrySection,
    CrewTraining_LogBookEntrySection,
    Engineer_LogBookEntrySection,
    Engine_LogBookEntrySection,
    Fuel_LogBookEntrySection,
    Ports_LogBookEntrySection,
    Supernumerary_LogBookEntrySection,
    VesselDailyCheck_LogBookEntrySection,
    VoyageSummary_LogBookEntrySection,
    CrewWelfare_LogBookEntrySection,
    LogBookSignOff_LogBookEntrySection,
    GET_LOGBOOK_CONFIG,
    GET_SECTION_MEMBER_COMMENTS,
    GET_LOGBOOK_ENTRY_BY_ID,
    GetCrewMembersFromOpenLogBook,
    TripReport_LogBookEntrySection_Brief,
    GET_INFRINGEMENTNOTICES,
    ReadWeatherForecasts,
    ReadWeatherObservations,
} from '@/app/lib/graphQL/query'
import React from 'react'
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import Image from 'next/image'
import {
    GetLogBookEntriesMembers,
    getOneClient,
    getSignatureUrl,
    getVesselByID,
} from '@/app/lib/actions'
import { formatDate, formatDateTime } from '@/app/helpers/dateHelper'
import { SLALL_LogBookFields } from '@/app/lib/logbook-configuration'
import dayjs from 'dayjs'
import {
    displayField,
    displaySignOffField,
    getFieldLabel,
    getSignOffFieldLabel,
} from '@/app/ui/daily-checks/actions'
import Loading from '@/app/loading'
import {
    getCrewWelfareFields,
    getEngine,
    getHull,
    getNavigation,
    getSafety,
    getSignOff,
} from '@/app/lib/dailyCheckFields'
import { camelCase } from 'lodash'
import SignatureImage from '@/components/signature-image'
import { Button } from '@/components/ui'
import { cn } from '@/app/lib/utils'

export default function Page() {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0

    const [isLoading, setIsLoading] = useState(true)
    const router = useRouter()
    const [master, setMaster] = useState<any>()
    const [client, setClient] = useState<any>()
    const [logbook, setLogbook] = useState<any>()
    const [vessel, setVessel] = useState<any>()
    const [crew, setCrew] = useState<any>()
    const [duties, setDuties] = useState<any>()
    const [allCrew, setAllCrew] = useState<any>()
    const [loaded, setLoaded] = useState(false)
    const [initialized, setInitialized] = useState(false)
    const [locked, setLocked] = useState(false)
    const [logEntrySections, setLogEntrySections] = useState<any>()
    const [engine, setEngine] = useState<any>()
    const [fuel, setFuel] = useState<any>()
    const [ports, setPorts] = useState<any>()
    const [vesselDailyCheck, setVesselDailyCheck] = useState<any>(false)
    const [signOff, setSignOff] = useState<any>()
    const [voyageSummary, setVoyageSummary] = useState<any>()
    const [tripReport, setTripReport] = useState<any>()
    const [crewMembers, setCrewMembers] = useState<any>()
    const [crewTraining, setCrewTraining] = useState<any>()
    const [supernumerary, setSupernumerary] = useState<any>()
    const [engineer, setEngineer] = useState<any>()
    const [assetReporting, setAssetReporting] = useState<any>()
    const [crewWelfare, setCrewWelfare] = useState<any>()
    const [crewMembersList, setCrewMembersList] = useState<any>()
    const [signOffComment, setSignOffComment] = useState<any>()
    const [dailyCheckComments, setDailyCheckComments] = useState<any>()
    const [crewWelfareComment, setCrewWelfareComment] = useState<any>()
    const [logBookConfig, setLogBookConfig] = useState<any>(false)
    const [dailyCheckCombined, setDailyCheckCombined] = useState<any>(false)
    const [infringementNotices, setInfringementNotices] = useState<any>(false)
    const [weatherForecasts, setWeatherForecasts] = useState<any>([])
    const [weatherObservations, setWeatherObservations] = useState<any>([])
    const [loadedChecks, setLoadedChecks] = useState<any>(() => {
        const initialState = {
            checks: false,
            client: false,
            config: false,
            crew_members: false,
            daily_checks: false,
            daily_comments: false,
            sign_off: false,
            sign_off_comments: false,
            trip_report: false,
            vessel: false,
            voyage_summary: false,
            weather_forecasts: false,
            weather_observations: false,
        }
        console.log('🏁 Initial loadedChecks state:', initialState)
        return initialState
    })
    const [signOffEmbeddedFields, setSignOffEmbeddedFields] =
        useState<any>(false)
    const [countdown, setCountdown] = useState<number>(5)
    const [showCountdown, setShowCountdown] = useState<boolean>(false)

    const handleSetVessel = (vessel: any) => {
        console.log(
            '🚢 Setting vessel data, updating loadedChecks.vessel to true',
        )
        setVessel(vessel)
        setLoadedChecks({ ...loadedChecks, vessel: true })
    }

    const displayEventTypeField = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    try {
        getVesselByID(+vesselID, handleSetVessel)
    } catch (error) {
        console.error('Error in getVesselByID:', error)
    }
    const handleSetClient = (client: any) => {
        console.log(
            '👤 Setting client data, updating loadedChecks.client to true',
        )
        setClient(client)
        setLoadedChecks({ ...loadedChecks, client: true })
    }
    try {
        getOneClient(handleSetClient)
    } catch (error) {
        console.error('Error in getOneClient:', error)
    }

    const [queryLogBookConfig] = useConfiguredLazyQuery({
        query: GET_LOGBOOK_CONFIG,
        label: 'LogBookConfig',
        extract: (r) => r.readOneCustomisedLogBookConfig,
        onData: (data) => {
            if (data) {
                console.log(
                    '⚙️ Setting logbook config, updating loadedChecks.config to true',
                )
                setLogBookConfig(data)
                setLoadedChecks({ ...loadedChecks, config: true })
            }
        },
    })
    const [getSectionEngine_LogBookEntrySection] = useConfiguredLazyQuery({
        query: Engine_LogBookEntrySection,
        label: 'EngineSection',
        extract: (r) => r.readEngine_LogBookEntrySections.nodes,
        onData: setEngine,
    })
    const [getSectionFuel_LogBookEntrySection] = useConfiguredLazyQuery({
        query: Fuel_LogBookEntrySection,
        label: 'FuelSection',
        extract: (r) => r.readFuel_LogBookEntrySections.nodes,
        onData: setFuel,
    })
    const [getSectionPorts_LogBookEntrySection] = useConfiguredLazyQuery({
        query: Ports_LogBookEntrySection,
        label: 'PortsSection',
        extract: (r) => r.readPorts_LogBookEntrySections.nodes,
        onData: setPorts,
    })
    const [getSectionVesselDailyCheck_LogBookEntrySection] =
        useConfiguredLazyQuery({
            query: VesselDailyCheck_LogBookEntrySection,
            label: 'DailyCheckSection',
            extract: (r) =>
                r.readVesselDailyCheck_LogBookEntrySections.nodes[0],
            onData: (section) => {
                console.log(
                    '📋 Setting vessel daily check, updating loadedChecks.daily_checks to true',
                )
                setVesselDailyCheck(section)
                setLoadedChecks({ ...loadedChecks, daily_checks: true })
                queryDailyCheckMemberComments({
                    variables: {
                        filter: {
                            logBookEntrySectionID: { eq: section.id },
                        },
                    },
                })
            },
        })
    const [getLogBookSignOff_LogBookEntrySection] = useConfiguredLazyQuery({
        query: LogBookSignOff_LogBookEntrySection,
        label: 'LogBookSignOffSection',
        extract: (r) => r.readLogBookSignOff_LogBookEntrySections.nodes[0],
        onData: (section) => {
            console.log(
                '📝 Setting sign off section, updating loadedChecks.sign_off to true',
            )
            setSignOff(section)
            setLoadedChecks({ ...loadedChecks, sign_off: true })
            console.log('🔍 Querying section member comments for sign off')
            querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: section.id },
                    },
                },
            })
        },
    })
    const [querySectionMemberComments] = useConfiguredLazyQuery({
        query: GET_SECTION_MEMBER_COMMENTS,
        label: 'SectionMemberComments',
        extract: (r) => r.readSectionMemberComments.nodes,
        onData: (data) => {
            console.log(
                '💬 Section member comments query completed, data:',
                data ? 'found' : 'empty',
            )
            if (data) {
                setSignOffComment(data)
            }
            // Always set sign_off_comments to true whether data exists or not
            console.log('✅ Setting sign_off_comments to true')
            setLoadedChecks({
                ...loadedChecks,
                sign_off_comments: true,
            })
        },
    })
    const [queryDailyCheckMemberComments] = useConfiguredLazyQuery({
        query: GET_SECTION_MEMBER_COMMENTS,
        label: 'DailyCheckMemberComments',
        extract: (r) => r.readSectionMemberComments.nodes,
        onData: (data) => {
            if (data) {
                setDailyCheckComments(data)
                setLoadedChecks({ ...loadedChecks, daily_comments: true })
            }
        },
    })
    const [getSectionVoyageSummary_LogBookEntrySection] =
        useConfiguredLazyQuery({
            query: VoyageSummary_LogBookEntrySection,
            label: 'VoyageSummarySection',
            extract: (r) => r.readVoyageSummary_LogBookEntrySections.nodes,
            onData: (data) => {
                setVoyageSummary(data)
                setLoadedChecks({ ...loadedChecks, voyage_summary: true })
            },
        })

    const [getInfringements] = useConfiguredLazyQuery({
        query: GET_INFRINGEMENTNOTICES,
        label: 'Infringements',
        extract: (r) => r.readInfringementNotices.nodes,
        onData: setInfringementNotices,
    })

    const [getWeatherForecasts] = useConfiguredLazyQuery({
        query: ReadWeatherForecasts,
        label: 'WeatherForecasts',
        extract: (r) => r.readWeatherForecasts.nodes,
        onData: (data) => {
            console.log(
                '🌤️ Setting weather forecasts, updating loadedChecks.weather_forecasts to true',
            )
            setWeatherForecasts(data)
            setLoadedChecks({ ...loadedChecks, weather_forecasts: true })
        },
    })

    const [getWeatherObservations] = useConfiguredLazyQuery({
        query: ReadWeatherObservations,
        label: 'WeatherObservations',
        extract: (r) => r.readWeatherObservations.nodes,
        onData: (data) => {
            console.log(
                '🌦️ Weather observations query completed, data:',
                data ? `${data.length} items` : 'empty',
            )
            setWeatherObservations(data || [])
            // Always set weather_observations to true whether data exists or not
            console.log('✅ Setting weather_observations to true')
            setLoadedChecks({ ...loadedChecks, weather_observations: true })
        },
    })

    const [getSectionTripReport_LogBookEntrySection] = useConfiguredLazyQuery({
        query: TripReport_LogBookEntrySection_Brief,
        label: 'TripReportSection',
        extract: (r) => r.readTripReport_LogBookEntrySections.nodes,
        onData: (data) => {
            console.log(
                '🚢 Setting trip report, updating loadedChecks.trip_report to true',
            )
            setTripReport(data)
            setLoadedChecks({ ...loadedChecks, trip_report: true })
            const infringementIds = data
                .map((item: any) =>
                    item.tripEvents.nodes.map(
                        (event: any) => event.infringementNoticeID,
                    ),
                )
                .flat()
                .filter((id: any) => id && id !== 0)
            infringementIds.length > 0 &&
                getInfringements({ variables: { id: infringementIds } })
        },
    })

    const [getSectionCrewMembers_LogBookEntrySection] = useConfiguredLazyQuery({
        query: CrewMembers_LogBookEntrySection,
        label: 'CrewMembersSection',
        extract: (r) => r.readCrewMembers_LogBookEntrySections.nodes,
        onData: (data) => {
            console.log(
                '👥 Setting crew members, updating loadedChecks.crew_members to true',
            )
            setCrewMembers(data)
            setLoadedChecks({ ...loadedChecks, crew_members: true })
        },
    })

    const handleSetCrewMembers = (crewMembers: any) => {
        const crewMemberList = crewMembers.filter(
            (item: any) =>
                !logEntrySections
                    ?.filter(
                        (item: any) =>
                            item.className ===
                            'SeaLogs\\CrewMembers_LogBookEntrySection',
                    )
                    ?.flatMap((item: any) => +item.ids)
                    ?.includes(item),
        )
        if (crewMemberList && crewMemberList.length > 0) {
            getCrewMembersFromOpenLogBook({
                variables: {
                    ids: crewMemberList,
                },
            })
        }
    }
    const [getCrewMembersFromOpenLogBook] = useConfiguredLazyQuery({
        query: GetCrewMembersFromOpenLogBook,
        label: 'CrewMembersFromOpenLogBook',
        extract: (r) => r.readCrewMembers_LogBookEntrySections.nodes,
        onData: (data) => {
            setCrewMembersList(
                Array.from(
                    new Set(
                        data
                            .flatMap((item: any) => item.crewMember)
                            .flatMap((item: any) => +item.id),
                    ),
                ),
            )
        },
    })

    GetLogBookEntriesMembers(handleSetCrewMembers)

    const [getSectionCrewTraining_LogBookEntrySection] = useConfiguredLazyQuery(
        {
            query: CrewTraining_LogBookEntrySection,
            label: 'CrewTrainingSection',
            extract: (r) => r.readCrewTraining_LogBookEntrySections.nodes,
            onData: setCrewTraining,
        },
    )
    const [getSectionSupernumerary_LogBookEntrySection] =
        useConfiguredLazyQuery({
            query: Supernumerary_LogBookEntrySection,
            label: 'SupernumerarySection',
            extract: (r) => r.readSupernumerary_LogBookEntrySections.nodes,
            onData: setSupernumerary,
        })
    const [getSectionEngineer_LogBookEntrySection] = useConfiguredLazyQuery({
        query: Engineer_LogBookEntrySection,
        label: 'EngineerSection',
        extract: (r) => r.readEngineer_LogBookEntrySections.nodes,
        onData: setEngineer,
    })
    const [getSectionAssetReporting_LogBookEntrySection] =
        useConfiguredLazyQuery({
            query: AssetReporting_LogBookEntrySection,
            label: 'AssetReportingSection',
            extract: (r) => r.readAssetReporting_LogBookEntrySections.nodes,
            onData: setAssetReporting,
        })
    const [getSectionCrewWelfare_LogBookEntrySection] = useConfiguredLazyQuery({
        query: CrewWelfare_LogBookEntrySection,
        label: 'CrewWelfareSection',
        extract: (r) => r.readCrewWelfare_LogBookEntrySections.nodes,
        onData: (data) => {
            setCrewWelfare(data)
            querySectionMemberCWComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: data[0].id },
                    },
                },
            })
        },
    })

    const [querySectionMemberCWComments] = useConfiguredLazyQuery({
        query: GET_SECTION_MEMBER_COMMENTS,
        label: 'CrewWelfareComments',
        extract: (r) => r.readSectionMemberComments.nodes,
        onData: (data) => {
            if (data) {
                setCrewWelfareComment(data)
            }
        },
    })

    const handleSetLogbook = (logbook: any) => {
        console.log(
            '📖 Setting logbook data, updating loadedChecks.master and logbook to true',
        )
        setLogbook(logbook)
        setMaster(logbook.master)
        setLoadedChecks({ ...loadedChecks, master: true, logbook: true })
        queryLogBookConfig({
            variables: {
                id: logbook.logBook.id,
            },
        })
        logbook.state === 'Locked' ? setLocked(true) : setLocked(false)
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))

        setLogEntrySections(sectionTypes)
        console.log(
            '📋 Available section types:',
            sectionTypes.map((s) => s.className),
        )

        if (
            sectionTypes.find(
                (section: any) =>
                    section.className ===
                    'SeaLogs\\TripReport_LogBookEntrySection',
            )
        ) {
            console.log(
                '📝 Found TripReport section, setting daily_checks and daily_comments to true',
            )
            setLoadedChecks({
                ...loadedChecks,
                daily_checks: true,
                daily_comments: true,
            })
        }

        if (
            sectionTypes.find(
                (section: any) =>
                    section.className ===
                    'SeaLogs\\LogBookSignOff_LogBookEntrySection',
            )
        ) {
            setLoadedChecks({
                ...loadedChecks,
                sign_off: true,
                sign_off_comments: true,
            })
        }

        if (
            sectionTypes.find(
                (section: any) =>
                    section.className ===
                    'SeaLogs\\VoyageSummary_LogBookEntrySection',
            )
        ) {
            setLoadedChecks({
                ...loadedChecks,
                voyage_summary: true,
            })
        }

        sectionTypes.forEach((section: any) => {
            if (section.className === 'SeaLogs\\Engine_LogBookEntrySection') {
                getSectionEngine_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (section.className === 'SeaLogs\\Fuel_LogBookEntrySection') {
                getSectionFuel_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (section.className === 'SeaLogs\\Ports_LogBookEntrySection') {
                getSectionPorts_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (
                section.className ===
                'SeaLogs\\VesselDailyCheck_LogBookEntrySection'
            ) {
                setLoadedChecks({
                    ...loadedChecks,
                    daily_checks: false,
                    daily_comments: false,
                })
                getSectionVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (
                section.className ===
                'SeaLogs\\LogBookSignOff_LogBookEntrySection'
            ) {
                console.log(
                    '📝 Found LogBookSignOff section, calling getLogBookSignOff_LogBookEntrySection',
                )
                setLoadedChecks({
                    ...loadedChecks,
                    sign_off: false,
                    sign_off_comments: false,
                })
                getLogBookSignOff_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }

            if (
                section.className === 'SeaLogs\\CrewWelfare_LogBookEntrySection'
            ) {
                getSectionCrewWelfare_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (
                section.className ===
                'SeaLogs\\VoyageSummary_LogBookEntrySection'
            ) {
                setLoadedChecks({
                    ...loadedChecks,
                    voyage_summary: false,
                })
                getSectionVoyageSummary_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                console.log(
                    '🚢 Found TripReport section, calling getSectionTripReport_LogBookEntrySection',
                )
                getSectionTripReport_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (
                section.className === 'SeaLogs\\CrewMembers_LogBookEntrySection'
            ) {
                const searchFilter: SearchFilter = {}
                searchFilter.id = { in: section.ids }

                getSectionCrewMembers_LogBookEntrySection({
                    variables: {
                        filter: searchFilter,
                    },
                })
            }
            if (
                section.className ===
                'SeaLogs\\CrewTraining_LogBookEntrySection'
            ) {
                getSectionCrewTraining_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (
                section.className ===
                'SeaLogs\\Supernumerary_LogBookEntrySection'
            ) {
                getSectionSupernumerary_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (section.className === 'SeaLogs\\Engineer_LogBookEntrySection') {
                getSectionEngineer_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
            if (
                section.className ===
                'SeaLogs\\AssetReporting_LogBookEntrySection'
            ) {
                getSectionAssetReporting_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
        })

        // Set loadedChecks to true for sections that don't exist in this logbook
        const sectionClassNames = sectionTypes.map((s: any) => s.className)
        const missingChecks: any = {}

        // Check if VoyageSummary section exists
        if (
            !sectionClassNames.includes(
                'SeaLogs\\VoyageSummary_LogBookEntrySection',
            )
        ) {
            console.log(
                '📝 VoyageSummary section not found, setting voyage_summary to true',
            )
            missingChecks.voyage_summary = true
        }

        // Check if TripReport section exists
        if (
            !sectionClassNames.includes(
                'SeaLogs\\TripReport_LogBookEntrySection',
            )
        ) {
            console.log(
                '📝 TripReport section not found, setting trip_report to true',
            )
            missingChecks.trip_report = true
        }

        // Check if LogBookSignOff section exists (for sign_off_comments)
        if (
            !sectionClassNames.includes(
                'SeaLogs\\LogBookSignOff_LogBookEntrySection',
            )
        ) {
            console.log(
                '📝 LogBookSignOff section not found, setting sign_off_comments to true',
            )
            missingChecks.sign_off_comments = true
        }

        // Update loadedChecks for missing sections
        if (Object.keys(missingChecks).length > 0) {
            console.log('📝 Setting missing sections to true:', missingChecks)
            setLoadedChecks((prevChecks: any) => ({
                ...prevChecks,
                ...missingChecks,
            }))
        }

        // Set a timeout fallback for config loading (in case the query fails silently)
        setTimeout(() => {
            setLoadedChecks((prevChecks: any) => {
                if (!prevChecks.config) {
                    console.log(
                        '⏰ Config timeout - setting config to true as fallback',
                    )
                    return { ...prevChecks, config: true }
                }
                return prevChecks
            })
        }, 2000) // 2 second timeout

        // Set a timeout fallback for weather observations (in case no weather data exists)
        setTimeout(() => {
            setLoadedChecks((prevChecks: any) => {
                if (!prevChecks.weather_observations) {
                    console.log(
                        '⏰ Weather observations timeout - setting to true as fallback',
                    )
                    return { ...prevChecks, weather_observations: true }
                }
                return prevChecks
            })
        }, 3000) // 3 second timeout for weather data

        // Set timeout fallbacks for all remaining checks that might not complete
        setTimeout(() => {
            setLoadedChecks((prevChecks: any) => {
                const updates: any = {}

                if (!prevChecks.sign_off) {
                    console.log(
                        '⏰ Sign off timeout - setting to true as fallback',
                    )
                    updates.sign_off = true
                }

                if (!prevChecks.sign_off_comments) {
                    console.log(
                        '⏰ Sign off comments timeout - setting to true as fallback',
                    )
                    updates.sign_off_comments = true
                }

                if (!prevChecks.trip_report) {
                    console.log(
                        '⏰ Trip report timeout - setting to true as fallback',
                    )
                    updates.trip_report = true
                }

                if (!prevChecks.voyage_summary) {
                    console.log(
                        '⏰ Voyage summary timeout - setting to true as fallback',
                    )
                    updates.voyage_summary = true
                }

                if (Object.keys(updates).length > 0) {
                    console.log('⏰ Setting timeout fallbacks:', updates)
                    return { ...prevChecks, ...updates }
                }

                return prevChecks
            })
        }, 5000) // 5 second timeout for all remaining checks

        setLoaded(true)

        // Load weather data for this logbook entry
        loadWeatherData(logbook.id)
    }

    const loadWeatherData = (logBookEntryID: number) => {
        // Load weather forecasts
        getWeatherForecasts({
            variables: {
                filter: {
                    logBookEntryID: { eq: logBookEntryID },
                },
            },
        })

        // Load weather observations
        getWeatherObservations({
            variables: {
                filter: {
                    logBookEntryID: { eq: logBookEntryID },
                },
            },
        })
    }

    const [queryLogBookEntry] = useConfiguredLazyQuery({
        query: GET_LOGBOOK_ENTRY_BY_ID,
        label: 'LogBookEntry',
        extract: (r) => r.readOneLogBookEntry,
        onData: (data) => {
            if (data) {
                handleSetLogbook(data)
            }
        },
    })
    const getLogBookEntryByID = async (id: number) => {
        queryLogBookEntry({
            variables: {
                logbookEntryId: +id,
            },
        })
    }

    useEffect(() => {
        getLogBookEntryByID(+logentryID)
    }, [])

    // Debug useEffect to track all loadedChecks changes
    useEffect(() => {
        console.log('🔄 LoadedChecks state changed:', loadedChecks)
        const trueChecks = Object.entries(loadedChecks)
            .filter(([key, value]) => value === true)
            .map(([key]) => key)
        const falseChecks = Object.entries(loadedChecks)
            .filter(([key, value]) => value === false)
            .map(([key]) => key)
        console.log('✅ Checks that are true:', trueChecks)
        console.log('❌ Checks that are false:', falseChecks)
        console.log(
            '📊 Progress:',
            `${trueChecks.length}/${Object.keys(loadedChecks).length} checks completed`,
        )
    }, [loadedChecks])

    const handleData = () => {
        const logReportElement = document.getElementById('logReport')
        const crew_section = document.getElementById('crew_section')
        const daily_checks = document.getElementById('daily_checks')
        const trip_report = document.getElementById('trip_report')
        if (crew_section && daily_checks) {
            if (
                crew_section?.clientHeight + daily_checks?.clientHeight >
                2240
            ) {
                crew_section?.style.setProperty('min-height', '2240px')
            }
        }
        if (trip_report && trip_report.clientHeight > 1920) {
            trip_report.style.setProperty('min-height', '2240px')
            const reportChildren = Array.from(trip_report.children)
            let currentHeight = 0
            let currentGroup = document.createElement('div')
            currentGroup.className = 'page_break min-h-[2240px]'
            reportChildren.forEach((child) => {
                const childHeight = child.clientHeight
                if (currentHeight + childHeight > 1920) {
                    // Start new group
                    trip_report.appendChild(currentGroup)
                    currentGroup = document.createElement('div')
                    currentGroup.className = 'page_break min-h-[2240px] mt-8'
                    currentHeight = 0
                }
                currentGroup.appendChild(child)
                currentHeight += childHeight
            })
            // Add final group
            if (currentGroup.children.length > 0) {
                trip_report.appendChild(currentGroup)
            }
            trip_report.removeAttribute('id')
        }
        logReportElement?.style.setProperty('width', '1600px')
        if (logReportElement) {
            logReportElement?.style.setProperty('width', '1600px')

            // Generate PDF in image format.
            html2canvas(logReportElement, {
                scale: 1,
                useCORS: true,
                allowTaint: true,
                logging: true,
                windowWidth: logReportElement.scrollWidth,
                windowHeight: logReportElement.scrollHeight,
            })
                .then((canvas) => {
                    const imgData = canvas.toDataURL('image/png')
                    const pdf = new jsPDF('p', 'mm', [1400, 1979], true)
                    const imgProps = pdf.getImageProperties(imgData)
                    const pdfWidth = pdf.internal.pageSize.getWidth()
                    const pdfHeight =
                        (imgProps.height * pdfWidth) / imgProps.width
                    let position = 0
                    while (position < canvas.height - 1979) {
                        if (position !== 0) {
                            pdf.addPage()
                        }
                        pdf.addImage(
                            imgData,
                            'PNG',
                            0,
                            -position,
                            pdfWidth,
                            pdfHeight,
                        )
                        position += 1979
                    }

                    pdf.save('report.pdf')

                    // router.push(
                    //     `/log-entries?vesselID=${+vesselID}&logentryID=${+logentryID}`,
                    // )
                    logReportElement.style.width = '100%'
                })
                .catch((error) => {
                    console.error('Error generating PDF:', error)
                    alert(
                        'Error generating PDF. Please check the console for details.',
                    )
                })
        } else {
            console.error('logReportElement not found')
            alert('Could not find the report element to generate PDF')
        }
    }

    useEffect(() => {
        console.log('🔍 LoadedChecks useEffect triggered')
        console.log('📊 Current loadedChecks state:', loadedChecks)

        if (loadedChecks) {
            // Log individual check states
            console.log('📋 Individual check states:')
            Object.entries(loadedChecks).forEach(([key, value]) => {
                console.log(`  ${key}: ${value}`)
            })

            loadedChecks.checks === false &&
                setLoadedChecks({ ...loadedChecks, checks: true })

            const loadedChecksArray = Object.values(loadedChecks).some(
                (value) => value === false,
            )

            console.log(
                '🎯 loadedChecksArray (has false values):',
                loadedChecksArray,
            )
            console.log(
                '✅ All checks loaded (!loadedChecksArray):',
                !loadedChecksArray,
            )

            // Log which checks are still false
            const falseChecks = Object.entries(loadedChecks)
                .filter(([key, value]) => value === false)
                .map(([key]) => key)
            console.log('❌ Checks still false:', falseChecks)

            if (!loadedChecksArray) {
                console.log(
                    '🚀 All checks loaded! Starting countdown in 1 second...',
                )
                setTimeout(() => {
                    console.log('⏰ Setting loaded=true and showCountdown=true')
                    setLoaded(true)
                    setShowCountdown(true)
                    const downloadPdf = document.querySelector('#downloadPdf')

                    if (downloadPdf) {
                        downloadPdf.remove()
                    }
                }, 1000)
            } else {
                console.log('⏳ Still waiting for checks to complete...')
            }
        } else {
            console.log('❗ loadedChecks is falsy')
        }
    }, [loadedChecks])

    // Countdown effect for PDF download
    useEffect(() => {
        if (showCountdown && countdown > 0) {
            const timer = setTimeout(() => {
                setCountdown(countdown - 1)
            }, 1000)
            return () => clearTimeout(timer)
        } else if (showCountdown && countdown === 0) {
            handleData()
            setShowCountdown(false)
        }
    }, [showCountdown, countdown])

    const dailyCheckTypes = [
        { title: 'Safety Checks', value: 'Safety' },
        { title: 'Engine Checks', value: 'Engine' },
        { title: 'Jet Specific Checks', value: 'JetSpecific' },
        { title: 'Cleaning Checks', value: 'Cleaning' },
        { title: 'Navigation', value: 'Navigation' },
        { title: 'Deck operations and exterior checks', value: 'Hull' },
        { title: 'HVAC', value: 'HVAC' },
        { title: 'Plumbing', value: 'Plumbing' },
        { title: 'Sail', value: 'Sail' },
        { title: 'Biosecurity', value: 'Biosecurity' },
    ]

    useEffect(() => {
        if (logBookConfig && vesselDailyCheck) {
            const dailyCheckFields = Object.entries(vesselDailyCheck)
                // .filter(([key, value]) => value === 'Ok' || value === 'Not_Ok')
                .map(([key, value]) => ({ key, value }))

            const allLocalItems: any = SLALL_LogBookFields.filter(
                (item: any) =>
                    item.componentClass === 'VesselDailyCheck_LogBookComponent',
            )[0].items
            const allSignOffLocalItems: any = SLALL_LogBookFields.filter(
                (item: any) =>
                    item.componentClass === 'LogBookSignOff_LogBookComponent',
            )[0].items

            const logBookConfigItems =
                logBookConfig?.customisedLogBookComponents?.nodes
                    ?.filter(
                        (item: any) =>
                            item.componentClass ===
                            'VesselDailyCheck_LogBookComponent',
                    )[0]
                    .customisedComponentFields.nodes.filter((item: any) =>
                        dailyCheckFields.some(
                            (field: any) =>
                                field.key === camelCase(item.fieldName),
                        ),
                    )

            const dailyCheckEmbeddedFields = logBookConfigItems.map(
                (item: any) => ({
                    ...item,
                    customisedFieldTitle: item.customisedFieldTitle,
                    fieldName: item.fieldName,
                    value: dailyCheckFields.find(
                        (field: any) => field.key === camelCase(item.fieldName),
                    )?.value,
                    fieldSet: allLocalItems.find(
                        (sitem: any) => sitem.value === item.fieldName,
                    )?.fieldSet,
                    groupTo: allLocalItems.filter(
                        (sitem: any) => sitem.value === item.fieldName,
                    )[0]?.groupTo,
                }),
            )
            if (dailyCheckEmbeddedFields.length > 0) {
                setDailyCheckCombined(dailyCheckEmbeddedFields)
            }
            if (signOff) {
                const signOffFields = Object.entries(signOff)
                    .filter(
                        ([key, value]) => value === 'Ok' || value === 'Not_Ok',
                    )
                    .map(([key, value]) => ({ key, value }))
                const logBookSignOffConfigItems =
                    logBookConfig?.customisedLogBookComponents?.nodes
                        ?.filter(
                            (item: any) =>
                                item.componentClass ===
                                'LogBookSignOff_LogBookComponent',
                        )[0]
                        .customisedComponentFields.nodes.filter((item: any) =>
                            signOffFields.some(
                                (field: any) =>
                                    field.key === camelCase(item.fieldName),
                            ),
                        )
                const signOffEmbeddedFields = logBookSignOffConfigItems.map(
                    (item: any) => ({
                        ...item,
                        customisedFieldTitle: item.customisedFieldTitle,
                        fieldName: item.fieldName,
                        value: signOffFields.find(
                            (field: any) =>
                                field.key === camelCase(item.fieldName),
                        )?.value,
                        fieldSet: 'Signoff',
                        groupTo: allSignOffLocalItems.filter(
                            (sitem: any) => sitem.value === item.fieldName,
                        )[0]?.groupTo,
                    }),
                )
                setSignOffEmbeddedFields(signOffEmbeddedFields)
            }
        }
    }, [vesselDailyCheck, logBookConfig])

    const getDailyCheckComment = (
        fieldName: string,
        commentType = 'FieldComment',
    ) => {
        const comment =
            dailyCheckComments?.length > 0
                ? dailyCheckComments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const getSignOffComment = (
        fieldName: string,
        commentType = 'FieldComment',
    ) => {
        const comment =
            signOffComment?.length > 0
                ? signOffComment.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : ''
    }

    const getTripPOB = (trip: any) => {
        let totalGuests = 0
        const supernumeraries = trip.tripEvents?.nodes?.filter((event: any) => {
            return event.eventCategory === 'Supernumerary'
        })
        if (supernumeraries.length > 0) {
            supernumeraries.forEach((s: any) => {
                totalGuests += s.supernumerary.totalGuest
            })
        }
        const paxJoined = trip.tripReport_Stops.nodes.reduce(
            (acc: number, stop: any) => {
                return acc + stop.paxJoined - stop.paxDeparted
            },
            0,
        )
        return trip?.pob + crewMembers?.length + totalGuests + paxJoined
    }

    const goodsTypes = [
        { label: 'Class 1 Explosives', value: '1' },
        { label: 'Class 2 Gases', value: '2' },
        { label: 'Class 2.1 - Flammable gases', value: '2.1' },
        { label: 'Class 2.2 - Non-Flammable Non-Toxic Gases', value: '2.2' },
        { label: 'Class 2.3 - Toxic Gases', value: '2.3' },
        { label: 'Class 3 Flammable Liquids', value: '3' },
        { label: 'Class 4 Flammable Solids', value: '4' },
        { label: 'Class 4.1 - Flammable Solids', value: '4.1' },
        {
            label: 'Class 4.2 - Spontaneously Combustible Substances',
            value: '4.2',
        },
        { label: 'Class 4.3 - Substances Flammable When Wet', value: '4.3' },
        {
            label: 'Class 5 Oxidizing Substances and Organic Peroxides',
            value: '5',
        },
        { label: 'Class 5.1 - Oxidising Substances', value: '5.1' },
        { label: 'Class 5.2 - Organic Peroxides', value: '5.2' },
        { label: 'Class 6 Toxic and Infectious Substances', value: '6' },
        { label: 'Class 6.1 - Toxic Substances', value: '6.1' },
        { label: 'Class 6.2 - Infectious Substances', value: '6.2' },
        { label: 'Class 7 Radioactive Substances', value: '7' },
        { label: 'Class 8 Corrosive Substances', value: '8' },
        { label: 'Class 9 Miscellaneous Hazardous Substance', value: '9' },
    ]

    const getActivityType = (activity: any, trip: any) => {
        if (activity?.parentTaskingID > 0) {
            return trip?.tripEvents?.nodes
                ?.filter(
                    (event: any) =>
                        event.eventType_Tasking.id === activity.parentTaskingID,
                )[0]
                ?.eventType_Tasking?.operationType?.replace(/_/g, ' ')
        }
        return activity?.operationType?.replace(/_/g, ' ')
    }

    const getTowingChecklist = (towing: any, trip: any) => {
        return trip.tripEvents.nodes.find(
            (event: any) =>
                event.eventType_Tasking.id === towing.parentTaskingID,
        ).eventType_Tasking.towingChecklist
    }

    const displayReportField = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'TripReport_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const displayWeatherField = (fieldName: string) => {
        // Check for Weather_LogBookComponent configuration
        const weatherComponent =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'Weather_LogBookComponent',
            )

        if (weatherComponent?.length > 0) {
            if (weatherComponent[0]?.active) {
                return true
            }
        }

        // Fallback: if no weather component configuration found, default to true for weather fields
        return true
    }

    const getFilteredItems = (items: any, dailyCheckCombined: any) => {
        const itemNames = items.map((item: any) => item.name)
        const groups = Array.from(
            new Set(dailyCheckCombined.map((item: any) => item.groupTo)),
        )
        return dailyCheckCombined
            .filter(
                (item: any) =>
                    itemNames.includes(item.fieldName) &&
                    !groups.includes(item.fieldName),
            )
            .map((item: any) => {
                return {
                    ...item,
                    customisedFieldTitle: items.find(
                        (i: any) => i.name === item.fieldName,
                    )?.label,
                }
            })
    }

    const getFilteredNotItems = (
        items: any,
        dailyCheckCombined: any,
        individual = false,
    ) => {
        const itemNames = items.filter(
            (item: any) =>
                item.checked !== 'Ok' && displayField(item.name, logBookConfig),
        )
        return itemNames
    }

    const dailyCheckNotOk = (type: any) => {
        switch (type.value) {
            case 'Hull':
                return (
                    <>
                        {getHull(
                            logBookConfig,
                            vesselDailyCheck,
                        )[0]?.group?.map((items: any) =>
                            getFilteredNotItems(items, dailyCheckCombined)
                                .filter(
                                    (item: any) =>
                                        !getDailyCheckComment(
                                            item.name,
                                            'FieldComment',
                                        ),
                                )
                                .map((item: any) => item.label)
                                .join(', '),
                        )}
                        {getFilteredNotItems(
                            getHull(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                            true,
                        )
                            .map((item: any) => item.label)
                            .join(', ')}
                    </>
                )

            case 'Engine':
                return (
                    <>
                        {getEngine(
                            logBookConfig,
                            vesselDailyCheck,
                        )[0]?.group?.map((items: any) =>
                            getFilteredNotItems(items, dailyCheckCombined)
                                .filter(
                                    (item: any) =>
                                        !getDailyCheckComment(
                                            item.name,
                                            'FieldComment',
                                        ),
                                )
                                .map((item: any) => item.label)
                                .join(', '),
                        )}
                        {getFilteredNotItems(
                            getEngine(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                            true,
                        )
                            .map((item: any) => item.label)
                            .join(', ')}
                    </>
                )

            case 'Safety':
                return (
                    <>
                        {getSafety(
                            logBookConfig,
                            vesselDailyCheck,
                        )[0]?.group?.map((items: any) =>
                            getFilteredNotItems(items, dailyCheckCombined)
                                .filter(
                                    (item: any) =>
                                        !getDailyCheckComment(
                                            item.name,
                                            'FieldComment',
                                        ),
                                )
                                .map((item: any) => item.label)
                                .join(', '),
                        )}
                        {getFilteredNotItems(
                            getSafety(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                            true,
                        )
                            .map((item: any) => item.label)
                            .join(', ')}
                    </>
                )

            case 'Navigation':
                return (
                    <>
                        {getNavigation(
                            logBookConfig,
                            vesselDailyCheck,
                        )[0]?.group?.map((items: any) =>
                            getFilteredNotItems(items, dailyCheckCombined)
                                .filter(
                                    (item: any) =>
                                        !getDailyCheckComment(
                                            item.name,
                                            'FieldComment',
                                        ),
                                )
                                .map((item: any) => item.label)
                                .join(', '),
                        )}
                        {getFilteredNotItems(
                            getNavigation(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                            true,
                        )
                            .map((item: any) => item.label)
                            .join(', ')}
                    </>
                )

            default:
                return dailyCheckCombined
                    ?.filter(
                        (item: any) =>
                            item.fieldSet === type.title &&
                            item.value === 'Not_Ok' &&
                            !getDailyCheckComment(item.fieldName),
                    )
                    .map((item: any) =>
                        getFieldLabel(item.fieldName, logBookConfig),
                    )
                    .join(', ')
        }
    }

    const dailyCheckOk = (type: any) => {
        switch (type.value) {
            case 'Hull':
                return (
                    <>
                        {getHull(logBookConfig, vesselDailyCheck)[0]
                            ?.group?.map((items: any) =>
                                getFilteredItems(items, dailyCheckCombined)
                                    ?.filter(
                                        (item: any) =>
                                            item.fieldSet === type.title &&
                                            item.value === 'Ok' &&
                                            !getDailyCheckComment(
                                                item.fieldName,
                                                'FieldComment',
                                            ),
                                    )
                                    .map(
                                        (item: any) =>
                                            item.customisedFieldTitle ??
                                            getFieldLabel(
                                                item.fieldName,
                                                logBookConfig,
                                            ),
                                    )
                                    .join(', '),
                            )
                            .filter((item) => item !== '')
                            .join(', ')}
                        {getFilteredItems(
                            getHull(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                        )
                            ?.filter(
                                (item: any) =>
                                    item.fieldSet === type.title &&
                                    item.value === 'Ok' &&
                                    !getDailyCheckComment(
                                        item.fieldName,
                                        'FieldComment',
                                    ),
                            )
                            .map((item: any) =>
                                getFieldLabel(item.fieldName, logBookConfig),
                            )
                            .join(', ')}
                    </>
                )

            case 'Engine':
                return (
                    <>
                        {getEngine(logBookConfig, vesselDailyCheck)[0]
                            ?.group?.map((items: any) =>
                                getFilteredItems(items, dailyCheckCombined)
                                    ?.filter(
                                        (item: any) =>
                                            item.fieldSet === type.title &&
                                            item.value === 'Ok' &&
                                            !getDailyCheckComment(
                                                item.fieldName,
                                                'FieldComment',
                                            ),
                                    )
                                    .map(
                                        (item: any) =>
                                            item.customisedFieldTitle ??
                                            getFieldLabel(
                                                item.fieldName,
                                                logBookConfig,
                                            ),
                                    )
                                    .join(', '),
                            )
                            .filter((item) => item !== '')
                            .join(', ')}
                        {getFilteredItems(
                            getEngine(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                        )
                            ?.filter(
                                (item: any) =>
                                    item.fieldSet === type.title &&
                                    item.value === 'Ok' &&
                                    !getDailyCheckComment(
                                        item.fieldName,
                                        'FieldComment',
                                    ),
                            )
                            .map((item: any) =>
                                getFieldLabel(item.fieldName, logBookConfig),
                            )
                            .join(', ')}
                    </>
                )

            case 'Safety':
                return (
                    <>
                        {getSafety(logBookConfig, vesselDailyCheck)[0]
                            ?.group?.map((items: any) =>
                                getFilteredItems(items, dailyCheckCombined)
                                    ?.filter(
                                        (item: any) =>
                                            item.fieldSet === type.title &&
                                            item.value === 'Ok' &&
                                            !getDailyCheckComment(
                                                item.fieldName,
                                                'FieldComment',
                                            ),
                                    )
                                    .map(
                                        (item: any) =>
                                            item.customisedFieldTitle ??
                                            getFieldLabel(
                                                item.fieldName,
                                                logBookConfig,
                                            ),
                                    )
                                    .join(', '),
                            )
                            .filter((item) => item !== '')
                            .join(', ')}
                        {getFilteredItems(
                            getSafety(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                        )
                            ?.filter(
                                (item: any) =>
                                    item.fieldSet === type.title &&
                                    item.value === 'Ok' &&
                                    !getDailyCheckComment(
                                        item.fieldName,
                                        'FieldComment',
                                    ),
                            )
                            .map((item: any) =>
                                getFieldLabel(item.fieldName, logBookConfig),
                            )
                            .join(', ')}
                    </>
                )

            case 'Navigation':
                return (
                    <>
                        {getNavigation(logBookConfig, vesselDailyCheck)[0]
                            ?.group?.map((items: any) =>
                                getFilteredItems(items, dailyCheckCombined)
                                    ?.filter(
                                        (item: any) =>
                                            item.fieldSet === type.title &&
                                            item.value === 'Ok' &&
                                            !getDailyCheckComment(
                                                item.fieldName,
                                                'FieldComment',
                                            ),
                                    )
                                    .map(
                                        (item: any) =>
                                            item.customisedFieldTitle ??
                                            getFieldLabel(
                                                item.fieldName,
                                                logBookConfig,
                                            ),
                                    )
                                    .join(', '),
                            )
                            .filter((item) => item !== '')
                            .join(', ')}
                        {getFilteredItems(
                            getNavigation(logBookConfig, vesselDailyCheck)[1]
                                ?.individual,
                            dailyCheckCombined,
                        )
                            ?.filter(
                                (item: any) =>
                                    item.fieldSet === type.title &&
                                    item.value === 'Ok' &&
                                    !getDailyCheckComment(
                                        item.fieldName,
                                        'FieldComment',
                                    ),
                            )
                            .map((item: any) =>
                                getFieldLabel(item.fieldName, logBookConfig),
                            )
                            .join(', ')}
                    </>
                )

            default:
                return dailyCheckCombined
                    ?.filter(
                        (item: any) =>
                            item.fieldSet === type.title &&
                            item.value === 'Ok' &&
                            !getDailyCheckComment(
                                item.fieldName,
                                'FieldComment',
                            ),
                    )
                    .map((item: any) =>
                        getFieldLabel(item.fieldName, logBookConfig),
                    )
                    .join(', ')
        }
    }

    const signoffCheckNotOk = () => {
        return (
            <>
                {getSignOff(logBookConfig, signOffEmbeddedFields)[1]
                    ?.individual?.filter(
                        (item: any) =>
                            item.checked !== 'Ok' &&
                            displaySignOffField(item.name, logBookConfig),
                    )
                    .map((item: any) => item.label)
                    .join(', ')}
            </>
        )
    }

    const signoffCheckOk = () => {
        return (
            <>
                {getFilteredItems(
                    getSignOff(logBookConfig, signOffEmbeddedFields)[1]
                        ?.individual,
                    signOffEmbeddedFields,
                )
                    ?.filter(
                        (item: any) =>
                            item.value === 'Ok' &&
                            !getDailyCheckComment(
                                item.fieldName,
                                'FieldComment',
                            ),
                    )
                    .map((item: any) =>
                        getSignOffFieldLabel(item.fieldName, logBookConfig),
                    )
                    .join(', ')}
            </>
        )
    }

    const getCWComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            crewWelfareComment?.length > 0
                ? crewWelfareComment.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const crewWelfareCheckNotOk = () => {
        return (
            <>
                {getCrewWelfareFields(crewWelfare[0])
                    ?.filter(
                        (item: any) =>
                            item.checked !== 'Ok' &&
                            !getCWComment(item.name, 'FieldComment'),
                    )
                    .map((item: any) => item.label)
                    .join(', ')}
            </>
        )
    }

    const crewWelfareCheckOk = () => {
        return (
            <>
                {getCrewWelfareFields(crewWelfare[0])
                    ?.filter(
                        (item: any) =>
                            item.checked === 'Ok' &&
                            !getCWComment(item.name, 'FieldComment'),
                    )
                    .map((item: any) => item.label)
                    .join(', ')}
            </>
        )
    }

    const infringementFields = [
        { lifeJacket: 'Life Jacket infringement issued' },
        {
            speedOrNavigation: 'Speed / navigation infringement issued',
        },
        { towing: 'Towing infringement issued' },
        {
            swimmingOrDiving: 'Swimming / diving infringement issued',
        },
        {
            mooring: 'Mooring / embarkation / ramps or jetty infringements',
        },
        { other: 'Other' },
        {
            FailingToCarryLifejackets:
                'Failing to carry accessible and sufficient lifejackets of appropriate size for each person on board vessel',
        },
        {
            FailingToWearLifejacketWhenInstructed:
                'Failing to wear properly secured lifejacket of appropriate size when instructed by person in charge of recreational vessel',
        },
        {
            FailingToEnsureLifejacketOnVessel6mOrLess:
                'Failing to ensure persons on recreational vessel 6 metres or less wear proper lifejacket when vessel is making way',
        },
        {
            FailingToEnsureLifejacketForChildrenOnVesselGreaterThan6m:
                'Failing to ensure persons 10 years or younger on recreational vessel greater than 6 metres wear proper lifejacket at all times',
        },
        {
            FailingToEnsureLifejacketOnJetBoat:
                'Failing to ensure persons on recreational jet boat wear proper lifejacket when making way',
        },
        {
            FailingToEnsureLifejacketInDanger:
                'Failing to ensure persons on recreational vessel wear proper lifejacket in situations of danger or risk',
        },
        {
            TowingWithoutLifejacketOver5Knots:
                'Towing person who is not wearing properly secured lifejacket of appropriate size from vessel at speed exceeding 5 knots, or being towed from vessel at speed exceeding 5 knots when not wearing properly secured lifejacket of appropriate size',
        },
        {
            UnsupervisedUnderagePersonOperatingVessel:
                'An unsupervised underage person operating powered vessel capable of exceeding 10 knots',
        },
        { Option9: 'Option 9' },
        {
            AllowingUnsupervisedUnderagePerson:
                'Allowing an unsupervised underage person to operate powered vessel capable of exceeding 10 knots',
        },
        {
            Exceeding5KnotRestriction50Metres:
                'Exceeding 5-knot speed restriction within 50 metres of vessel, floating structure, or person',
        },
        {
            Exceeding5KnotRestriction200MetresShore:
                'Exceeding 5-knot speed restriction within 200 metres of shore or structure',
        },
        {
            Exceeding5KnotRestriction200MetresFlagA:
                'Exceeding 5-knot speed restriction within 200 metres of vessel flying Flag A of International Code of Signals',
        },
        {
            Exceeding5KnotRestrictionBodyExtending:
                'Exceeding 5-knot speed restriction while person has part of body extending from powered vessel',
        },
        {
            BeingTowedExceeding5Knots:
                'Being towed at speed of more than 5 knots in restricted-speed locations',
        },
        {
            NavigatingWithoutDueCare:
                'Navigating vessel without due care and caution or at speed or in manner so as to endanger any person',
        },
        {
            FailingToKeepStarboard:
                'Failing to ensure vessel keeps to starboard side of river channel',
        },
        {
            FailingToGiveWayDownstream:
                'Failing to give way to vessel coming downstream when going upstream',
        },
        {
            OperatingInUnsafeConditions:
                'Operating vessel in conditions that do not permit safe operation',
        },
        {
            Exceeding5KnotLimitLakes:
                'Exceeding 5-knot speed limit on specified lakes in powered vessel',
        },
        {
            TowingExceeding5KnotsNoLookout:
                'Towing person from vessel at speed exceeding 5 knots without lookout of appropriate age',
        },
        {
            BeingTowedExceeding5KnotsNoLookout:
                'Being towed from vessel at speed exceeding 5 knots without lookout of appropriate age',
        },
        {
            TowingExceeding5KnotsNight:
                'Towing person from vessel between sunset and sunrise or in restricted visibility',
        },
        {
            BeingTowedExceeding5KnotsNight:
                'Being towed from vessel between sunset and sunrise',
        },
        {
            ParasailingFranktonArm:
                'Lake Wakatipu: operating vessel involved in parasailing in Frankton arm of lake',
        },
        {
            CreatingNuisance:
                'Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel',
        },
        {
            SwimmingNearJettyWithNoSwimmingSign:
                'Swimming, diving, jumping, or related activities from or within 50 metres of jetty or wharf with “no swimming” signage',
        },
        {
            SwimmingInHarbourmasterArea:
                'Swimming or diving in area identified by harbourmaster',
        },
        {
            EmbarkingOrDisembarkingWhileVesselIsMakingWay:
                'Embarking or disembarking while vessel is making way',
        },
        {
            AnchoringVesselInMannerThatObstructsPassageOfVessels:
                'Anchoring vessel in manner that obstructs passage of vessels or obstructs approach to wharf, pier, or jetty, or creates hazard to vessels at anchor',
        },
        {
            FailingToMoorVesselInSecureManner:
                'Failing to moor vessel in secure manner or without adequate or safe means of accessing vessel',
        },
        {
            PlacingObstructionInWatersLikelyToRestrictNavigation:
                'Placing obstruction in waters likely to restrict navigation, or cause injury, death, or damage',
        },
        {
            TyingVesselToNavigationAidWithoutPermission:
                'Tying vessel to navigation aid without permission',
        },
        {
            DamagingRemovingDefacingOrInterferingWithNavigationAid:
                'Damaging, removing, defacing, or otherwise interfering with navigation aid',
        },
        {
            ObstructingUseOfJettyWharfRampOrLaunchFacility:
                'Obstructing use of jetty, wharf, ramp, or launch facility owned or operated by Queenstown Lakes District Council',
        },
        {
            RefuellingVesselWithPassengersOnBoard:
                'Refuelling vessel with passengers on board',
        },
        {
            PermittingVesselToContinueAfterWaterSkiDropped:
                'Permitting vessel to continue onwards after water ski or similar object dropped by person being towed',
        },
        {
            FailingToEnsureWakeSafety:
                'Failing to ensure wake does not prevent people from safely using waterway, or does not cause danger or risk of danger, or does not cause risk of harm',
        },
        {
            CreatingNuisanceThroughVesselUse:
                'Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel',
        },
        {
            FailingToKeepVesselSeaworthy:
                'Failing to keep vessel in seaworthy condition or leaving vessel sunk, stranded, or abandoned',
        },
        {
            FailingToConductHotWorkSafely:
                'Failing to conduct hot work operations in accordance with Code of Safe Working Practices for Merchant Seafarers',
        },
        {
            FailingToTakeFirePrecautions:
                'Failing to take fire precautions before or during hot work operations',
        },
        {
            FailingToMaintainDangerousGoodsRecord:
                'Failing to maintain or make available written record of dangerous goods loaded or unloaded onto vessel',
        },
        {
            FailingToApplyForOrganisedWaterActivity:
                'Failing to apply to the harbourmaster when intending to conduct organised water activity',
        },
    ]

    const getInfringementsBody = (infringementID: any) => {
        if (!infringementNotices) {
            return null
        }
        const infringement = infringementNotices?.find(
            (notice: any) => notice.id === infringementID,
        )
        const infringementData = JSON.parse(infringement?.infringementData)

        let infringementBody = []
        let infringementUsed = []
        infringement.time && infringementBody.push({ Time: infringement.time })
        infringement?.geoLocation?.id > 0 &&
            infringementBody.push({ Location: infringement.geoLocation.title })
        infringement?.waterwaysOfficer?.id > 0 &&
            infringementBody.push({
                'Waterways officer': `${infringement.waterwaysOfficer.firstName} ${infringement.waterwaysOfficer.surname}`,
            })
        infringement?.vesselName &&
            infringementBody.push({ 'Vessel name': infringement.vesselName })
        infringement?.vesselType &&
            infringementBody.push({ 'Vessel type': infringement.vesselType })
        infringement?.vesselReg &&
            infringementBody.push({
                'Vessel registration': infringement.vesselReg,
            })
        infringement?.ownerFullName &&
            infringementBody.push({ 'Owner name': infringement.ownerFullName })
        infringement?.ownerEmail &&
            infringementBody.push({ 'Owner email': infringement.ownerEmail })
        infringement?.ownerAddress &&
            infringementBody.push({ Address: infringement.ownerAddress })
        infringement?.ownerDOB &&
            infringementBody.push({ DOB: infringement.ownerDOB })
        infringement?.ownerOccupation &&
            infringementBody.push({
                'Owner occupation': infringement.ownerOccupation,
            })
        infringement?.ownerPhone &&
            infringementBody.push({ 'Owner phone': infringement.ownerPhone })
        if (infringementData?.infringementUsed?.length > 0) {
            infringementUsed.push({
                'Infringement used': infringementData.infringementUsed
                    .map((item: any) => {
                        const field = infringementFields.find(
                            (field: any) => Object.keys(field)[0] === item,
                        )
                        return field ? field[item as keyof typeof field] : ''
                    })
                    .join(', '),
            })
            infringementData?.lifeJacket?.length > 0 &&
                infringementUsed.push({
                    'Life Jacket infringement issued':
                        infringementData.lifeJacket
                            .map((item: any) => {
                                const field = infringementFields.find(
                                    (field: any) =>
                                        Object.keys(field)[0] === item,
                                )
                                return field
                                    ? field[item as keyof typeof field]
                                    : ''
                            })
                            .join(', '),
                })
            infringementData?.mooring?.length > 0 &&
                infringementUsed.push({
                    'Mooring / embarkation / ramps or jetty infringements':
                        infringementData.mooring
                            .map((item: any) => {
                                const field = infringementFields.find(
                                    (field: any) =>
                                        Object.keys(field)[0] === item,
                                )
                                return field
                                    ? field[item as keyof typeof field]
                                    : ''
                            })
                            .join(', '),
                })
            infringementData?.speedOrNavigation?.length > 0 &&
                infringementUsed.push({
                    'Speed / navigation infringement issued':
                        infringementData.speedOrNavigation
                            .map((item: any) => {
                                const field = infringementFields.find(
                                    (field: any) =>
                                        Object.keys(field)[0] === item,
                                )
                                return field
                                    ? field[item as keyof typeof field]
                                    : ''
                            })
                            .join(', '),
                })
            infringementData?.swimmingOrDiving?.length > 0 &&
                infringementUsed.push({
                    'Swimming / diving infringement issued':
                        infringementData.swimmingOrDiving
                            .map((item: any) => {
                                const field = infringementFields.find(
                                    (field: any) =>
                                        Object.keys(field)[0] === item,
                                )
                                return field
                                    ? field[item as keyof typeof field]
                                    : ''
                            })
                            .join(', '),
                })
            infringementData?.towing?.length > 0 &&
                infringementUsed.push({
                    'Towing infringement issued': infringementData.towing
                        .map((item: any) => {
                            const field = infringementFields.find(
                                (field: any) => Object.keys(field)[0] === item,
                            )
                            return field
                                ? field[item as keyof typeof field]
                                : ''
                        })
                        .join(', '),
                })
            infringementData?.other?.length > 0 &&
                infringementUsed.push({
                    Other: infringementData.other
                        .map((item: any) => {
                            const field = infringementFields.find(
                                (field: any) => Object.keys(field)[0] === item,
                            )
                            return field
                                ? field[item as keyof typeof field]
                                : ''
                        })
                        .join(', '),
                })
        }

        return (
            <>
                <tbody>
                    {infringementBody.length > 0 &&
                        Array.from({
                            length: Math.ceil(infringementBody.length / 2),
                        }).map((_, index) => {
                            const item1: any = infringementBody[index * 2]
                            const item2: any = infringementBody[index * 2 + 1]
                            return (
                                <tr
                                    key={index}
                                    className=" border-b last:border-b-0">
                                    {item1 && (
                                        <td>
                                            <span className="w-48 inline-block">
                                                {Object.keys(item1)[0]}
                                            </span>
                                            {item1[Object.keys(item1)[0]]}
                                        </td>
                                    )}
                                    {item2 && (
                                        <td>
                                            <span className="w-48 inline-block">
                                                {Object.keys(item2)[0]}
                                            </span>
                                            {item2[Object.keys(item2)[0]]}
                                        </td>
                                    )}
                                </tr>
                            )
                        })}
                    {infringementUsed.length > 0 &&
                        Array.from({
                            length: Math.ceil(infringementUsed.length),
                        }).map((_, index) => {
                            const item: any = infringementUsed[index]
                            return (
                                <tr key={index}>
                                    <td>
                                        <span className="w-48 inline-block">
                                            {Object.keys(item)[0]}
                                        </span>
                                    </td>
                                    <td>{item[Object.keys(item)[0]]}</td>
                                </tr>
                            )
                        })}
                    {infringement?.otherDescription && (
                        <tr>
                            <td>
                                <span className="w-48 inline-block">
                                    Other description
                                </span>
                            </td>
                            <td>
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: infringement.otherDescription,
                                    }}
                                />
                            </td>
                        </tr>
                    )}
                </tbody>
            </>
        )
    }

    const tableClass =
        'w-full table-fixed [&_td]:text-start [&_th]:text-outer-space-600 [&_th]:text-start [&_td]:px-6 [&_td]:py-3 [&_th]:px-6 [&_th]:py-3 [&_tr]:border-outer-space-200 [&_tr:not(:first-child)]:border-t [&_thead]:border-b [&_thead]:border-outer-space-400'

    // Log UI render condition
    const allChecksLoaded = !Object.values(loadedChecks).some(
        (value) => value === false,
    )
    console.log('🎨 UI Render - loaded:', loaded)
    console.log('🎨 UI Render - allChecksLoaded:', allChecksLoaded)
    console.log('🎨 UI Render - showCountdown:', showCountdown)

    return (
        <>
            {loaded ? (
                <div>
                    <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        {allChecksLoaded ? (
                            showCountdown ? (
                                <div className="space-y-3">
                                    <p className="text-blue-800">
                                        PDF will download automatically in{' '}
                                        <span className="font-bold text-lg text-blue-600">
                                            {countdown}
                                        </span>{' '}
                                        second{countdown !== 1 ? 's' : ''}...
                                    </p>
                                </div>
                            ) : (
                                <p className="text-gray-700">
                                    If the PDF does not download automatically,{' '}
                                    <span
                                        onClick={handleData}
                                        className="inline-block underline cursor-pointer text-blue-600 hover:text-blue-800">
                                        click here
                                    </span>
                                </p>
                            )
                        ) : (
                            <div className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                                    <p className="text-blue-800 font-medium">
                                        Preparing PDF... Loading data components
                                    </p>
                                </div>

                                <div className="bg-white rounded-lg p-4 border border-blue-100">
                                    <div className="flex justify-between items-center mb-2">
                                        <span className="text-sm font-medium text-gray-700">
                                            Progress
                                        </span>
                                        <span className="text-sm text-gray-600">
                                            {
                                                Object.values(
                                                    loadedChecks,
                                                ).filter(Boolean).length
                                            }
                                            /13 components loaded
                                        </span>
                                    </div>
                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                        <div
                                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                            style={{
                                                width: `${(Object.values(loadedChecks).filter(Boolean).length / 13) * 100}%`,
                                            }}></div>
                                    </div>

                                    <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
                                        {Object.entries(loadedChecks).map(
                                            ([key, value]) => (
                                                <div
                                                    key={key}
                                                    className="flex items-center space-x-2">
                                                    <div
                                                        className={`w-2 h-2 rounded-full ${value ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                                                    <span
                                                        className={`${value ? 'text-green-700' : 'text-gray-500'} capitalize`}>
                                                        {key.replace(/_/g, ' ')}
                                                    </span>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                    {/* <Button id="downloadPdf" onClick={handleData}>
                                Download PDF
                            </Button> */}
                    <div id="logReport" className="w-full px-4">
                        <div className="page_break min-h-[2240px]">
                            <div id="crew_section">
                                <div className="flex justify-between items-center">
                                    <h5 className=" ">
                                        <span className="font-medium">
                                            Log Entry{' '}
                                        </span>
                                        {vessel?.title} -{' '}
                                        {logbook?.startDate
                                            ? formatDate(logbook.startDate)
                                            : ''}
                                    </h5>
                                    <Image
                                        src="/sealogs-horizontal-logo.png"
                                        alt=""
                                        width={220}
                                        height={50}
                                    />
                                </div>
                                <div className="border border-outer-space-400 rounded-lg overflow-hidden">
                                    <table className={tableClass}>
                                        <tbody>
                                            <tr>
                                                <th align="left">Company: </th>
                                                <td>{client?.title}</td>
                                                <th align="left">Vessel:</th>
                                                <td>{vessel?.title}</td>
                                            </tr>
                                            <tr>
                                                <th align="left">Master:</th>
                                                <td>
                                                    {master?.firstName}{' '}
                                                    {master?.surname}
                                                </td>
                                                <th align="left">
                                                    Sign off time:
                                                </th>
                                                <td>
                                                    {formatDate(
                                                        signOff?.created,
                                                    )}{' '}
                                                    {signOff?.completedTime}
                                                </td>
                                            </tr>
                                            {getSignOffComment(
                                                'LogBookSignOff',
                                                'Section',
                                            ) && (
                                                <tr>
                                                    <th align="left">
                                                        Sign off comment:
                                                    </th>
                                                    <td colSpan={3}>
                                                        {getSignOffComment(
                                                            'LogBookSignOff',
                                                            'Section',
                                                        ) &&
                                                            getSignOffComment(
                                                                'LogBookSignOff',
                                                                'Section',
                                                            )?.comment}
                                                    </td>
                                                </tr>
                                            )}
                                            <tr>
                                                <th align="left">
                                                    End location:
                                                </th>
                                                <td>
                                                    {signOff?.endLocation
                                                        ?.geoLocationID > 0
                                                        ? signOff?.endLocation
                                                              ?.geoLocation
                                                              .title
                                                        : signOff?.endLocation
                                                                ?.lat &&
                                                            signOff?.endLocation
                                                                ?.long
                                                          ? 'Lat - ' +
                                                            signOff?.endLocation
                                                                ?.lat +
                                                            ' Long - ' +
                                                            signOff?.endLocation
                                                                ?.long
                                                          : ''}
                                                </td>
                                            </tr>
                                        </tbody>

                                        <tfoot>
                                            <tr>
                                                <th align="left">Signature</th>
                                                <td colSpan={3}>
                                                    {signOff?.sectionSignature
                                                        ?.id > 0 && (
                                                        <SignatureImage
                                                            className="w-1/2"
                                                            id={
                                                                signOff
                                                                    ?.sectionSignature
                                                                    ?.id
                                                            }
                                                            alt=""
                                                        />
                                                    )}
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                {crewMembers &&
                                    crewMembers?.filter(
                                        (member: any) =>
                                            member.crewMemberID > 0,
                                    ).length > 0 && (
                                        <>
                                            <div className="mt-8 mb-4">
                                                <h5 className=" ">
                                                    Crew members
                                                </h5>
                                            </div>

                                            <div className="border border-outer-space-400 rounded-lg overflow-hidden">
                                                <table className={tableClass}>
                                                    <thead>
                                                        <tr>
                                                            <th>Name</th>
                                                            <th>Duty</th>
                                                            <th>Sign In</th>
                                                            <th>Sign Out</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {crewMembers
                                                            .filter(
                                                                (member: any) =>
                                                                    member.crewMemberID >
                                                                    0,
                                                            )
                                                            .map(
                                                                (
                                                                    member: any,
                                                                    i: number,
                                                                ) => (
                                                                    <tr
                                                                        key={`crew-section-${member.id}-${i}`}>
                                                                        <td>
                                                                            {
                                                                                member
                                                                                    .crewMember
                                                                                    .firstName
                                                                            }{' '}
                                                                            {
                                                                                member
                                                                                    .crewMember
                                                                                    .surname
                                                                            }
                                                                        </td>
                                                                        <td>
                                                                            {
                                                                                member
                                                                                    .dutyPerformed
                                                                                    .title
                                                                            }
                                                                        </td>
                                                                        <td>
                                                                            {formatDateTime(
                                                                                member.punchIn,
                                                                            )}
                                                                        </td>
                                                                        <td>
                                                                            {formatDateTime(
                                                                                member.punchOut,
                                                                            )}
                                                                        </td>
                                                                    </tr>
                                                                ),
                                                            )}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </>
                                    )}
                            </div>

                            {(() => {
                                const allWeatherData = [
                                    ...weatherForecasts,
                                    ...weatherObservations,
                                ]
                                return (
                                    allWeatherData.length > 0 && (
                                        <>
                                            {/* Check if any weather fields are enabled and have data */}
                                            {(allWeatherData.some(
                                                (item) => item?.windSpeed,
                                            ) ||
                                                allWeatherData.some(
                                                    (item) =>
                                                        item?.windDirection,
                                                ) ||
                                                allWeatherData.some(
                                                    (item) =>
                                                        item?.precipitation,
                                                ) ||
                                                allWeatherData.some(
                                                    (item) => item?.visibility,
                                                ) ||
                                                allWeatherData.some(
                                                    (item) => item?.swell,
                                                ) ||
                                                allWeatherData.some(
                                                    (item) => item?.pressure,
                                                ) ||
                                                allWeatherData.some(
                                                    (item) => item?.cloudCover,
                                                ) ||
                                                allWeatherData.some(
                                                    (item) => item?.comment,
                                                )) &&
                                                (displayWeatherField(
                                                    'WindSpeed',
                                                ) ||
                                                    displayWeatherField(
                                                        'WindDirection',
                                                    ) ||
                                                    displayWeatherField(
                                                        'Precipitation',
                                                    ) ||
                                                    displayWeatherField(
                                                        'Visibility',
                                                    ) ||
                                                    displayWeatherField(
                                                        'Swell',
                                                    ) ||
                                                    displayWeatherField(
                                                        'Pressure',
                                                    ) ||
                                                    displayWeatherField(
                                                        'CloudCover',
                                                    ) ||
                                                    displayWeatherField(
                                                        'Comment',
                                                    )) && (
                                                    <div id="weather_information">
                                                        <div className="mt-8 mb-4">
                                                            <h5>Weather</h5>
                                                        </div>

                                                        <div className="border border-outer-space-400 rounded-lg overflow-hidden">
                                                            <table
                                                                className={
                                                                    tableClass
                                                                }>
                                                                <thead>
                                                                    <tr>
                                                                        {allWeatherData.some(
                                                                            (
                                                                                item,
                                                                            ) =>
                                                                                item?.time ||
                                                                                item?.day,
                                                                        ) && (
                                                                            <th>
                                                                                Time/Day
                                                                            </th>
                                                                        )}
                                                                        {allWeatherData.some(
                                                                            (
                                                                                item,
                                                                            ) =>
                                                                                item
                                                                                    ?.geoLocation
                                                                                    ?.title,
                                                                        ) && (
                                                                            <th>
                                                                                Location
                                                                            </th>
                                                                        )}
                                                                        {((displayWeatherField(
                                                                            'WindSpeed',
                                                                        ) &&
                                                                            allWeatherData.some(
                                                                                (
                                                                                    item,
                                                                                ) =>
                                                                                    item?.windSpeed,
                                                                            )) ||
                                                                            (displayWeatherField(
                                                                                'WindDirection',
                                                                            ) &&
                                                                                allWeatherData.some(
                                                                                    (
                                                                                        item,
                                                                                    ) =>
                                                                                        item?.windDirection,
                                                                                ))) && (
                                                                            <th>
                                                                                Wind
                                                                            </th>
                                                                        )}
                                                                        {((displayWeatherField(
                                                                            'Precipitation',
                                                                        ) &&
                                                                            allWeatherData.some(
                                                                                (
                                                                                    item,
                                                                                ) =>
                                                                                    item?.precipitation,
                                                                            )) ||
                                                                            (displayWeatherField(
                                                                                'Visibility',
                                                                            ) &&
                                                                                allWeatherData.some(
                                                                                    (
                                                                                        item,
                                                                                    ) =>
                                                                                        item?.visibility,
                                                                                )) ||
                                                                            (displayWeatherField(
                                                                                'CloudCover',
                                                                            ) &&
                                                                                allWeatherData.some(
                                                                                    (
                                                                                        item,
                                                                                    ) =>
                                                                                        item?.cloudCover,
                                                                                ))) && (
                                                                            <th>
                                                                                Conditions
                                                                            </th>
                                                                        )}
                                                                        {displayWeatherField(
                                                                            'Swell',
                                                                        ) &&
                                                                            allWeatherData.some(
                                                                                (
                                                                                    item,
                                                                                ) =>
                                                                                    item?.swell,
                                                                            ) && (
                                                                                <th>
                                                                                    Swell
                                                                                </th>
                                                                            )}
                                                                        {displayWeatherField(
                                                                            'Pressure',
                                                                        ) &&
                                                                            allWeatherData.some(
                                                                                (
                                                                                    item,
                                                                                ) =>
                                                                                    item?.pressure,
                                                                            ) && (
                                                                                <th>
                                                                                    Pressure
                                                                                </th>
                                                                            )}
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {allWeatherData.map(
                                                                        (
                                                                            weatherItem,
                                                                            index,
                                                                        ) => {
                                                                            // Helper function to format wind data
                                                                            const formatWindData =
                                                                                (
                                                                                    item: any,
                                                                                ) => {
                                                                                    const windSpeed =
                                                                                        item?.windSpeed
                                                                                    const windDirection =
                                                                                        item?.windDirection

                                                                                    if (
                                                                                        windSpeed &&
                                                                                        windDirection
                                                                                    ) {
                                                                                        return `${windSpeed} ${windDirection}`
                                                                                    } else if (
                                                                                        windSpeed
                                                                                    ) {
                                                                                        return windSpeed
                                                                                    } else if (
                                                                                        windDirection
                                                                                    ) {
                                                                                        return `from ${windDirection}`
                                                                                    }
                                                                                    return '-'
                                                                                }

                                                                            // Helper function to format weather conditions
                                                                            const formatConditions =
                                                                                (
                                                                                    item: any,
                                                                                ) => {
                                                                                    const conditions =
                                                                                        []
                                                                                    if (
                                                                                        item?.precipitation
                                                                                    )
                                                                                        conditions.push(
                                                                                            item.precipitation,
                                                                                        )
                                                                                    if (
                                                                                        item?.visibility
                                                                                    )
                                                                                        conditions.push(
                                                                                            `${item.visibility} vis`,
                                                                                        )
                                                                                    if (
                                                                                        item?.cloudCover
                                                                                    )
                                                                                        conditions.push(
                                                                                            `${item.cloudCover} cloud`,
                                                                                        )
                                                                                    return conditions.length >
                                                                                        0
                                                                                        ? conditions.join(
                                                                                              ', ',
                                                                                          )
                                                                                        : '-'
                                                                                }

                                                                            return (
                                                                                <>
                                                                                    <tr
                                                                                        key={
                                                                                            index
                                                                                        }>
                                                                                        {allWeatherData.some(
                                                                                            (
                                                                                                item,
                                                                                            ) =>
                                                                                                item?.time ||
                                                                                                item?.day,
                                                                                        ) && (
                                                                                            <td
                                                                                                rowSpan={
                                                                                                    weatherItem?.comment
                                                                                                        ? 2
                                                                                                        : 1
                                                                                                }>
                                                                                                {(() => {
                                                                                                    if (
                                                                                                        weatherItem?.day &&
                                                                                                        weatherItem?.time
                                                                                                    ) {
                                                                                                        return `${weatherItem.day} ${weatherItem.time}`
                                                                                                    } else if (
                                                                                                        weatherItem?.day
                                                                                                    ) {
                                                                                                        return weatherItem.day
                                                                                                    } else if (
                                                                                                        weatherItem?.time
                                                                                                    ) {
                                                                                                        return weatherItem.time
                                                                                                    }
                                                                                                    return '-'
                                                                                                })()}
                                                                                            </td>
                                                                                        )}

                                                                                        {allWeatherData.some(
                                                                                            (
                                                                                                item,
                                                                                            ) =>
                                                                                                item
                                                                                                    ?.geoLocation
                                                                                                    ?.title,
                                                                                        ) && (
                                                                                            <td>
                                                                                                {weatherItem
                                                                                                    ?.geoLocation
                                                                                                    ?.title ||
                                                                                                    '-'}
                                                                                            </td>
                                                                                        )}

                                                                                        {((displayWeatherField(
                                                                                            'WindSpeed',
                                                                                        ) &&
                                                                                            allWeatherData.some(
                                                                                                (
                                                                                                    item,
                                                                                                ) =>
                                                                                                    item?.windSpeed,
                                                                                            )) ||
                                                                                            (displayWeatherField(
                                                                                                'WindDirection',
                                                                                            ) &&
                                                                                                allWeatherData.some(
                                                                                                    (
                                                                                                        item,
                                                                                                    ) =>
                                                                                                        item?.windDirection,
                                                                                                ))) && (
                                                                                            <td>
                                                                                                {formatWindData(
                                                                                                    weatherItem,
                                                                                                )}
                                                                                            </td>
                                                                                        )}
                                                                                        {((displayWeatherField(
                                                                                            'Precipitation',
                                                                                        ) &&
                                                                                            allWeatherData.some(
                                                                                                (
                                                                                                    item,
                                                                                                ) =>
                                                                                                    item?.precipitation,
                                                                                            )) ||
                                                                                            (displayWeatherField(
                                                                                                'Visibility',
                                                                                            ) &&
                                                                                                allWeatherData.some(
                                                                                                    (
                                                                                                        item,
                                                                                                    ) =>
                                                                                                        item?.visibility,
                                                                                                )) ||
                                                                                            (displayWeatherField(
                                                                                                'CloudCover',
                                                                                            ) &&
                                                                                                allWeatherData.some(
                                                                                                    (
                                                                                                        item,
                                                                                                    ) =>
                                                                                                        item?.cloudCover,
                                                                                                ))) && (
                                                                                            <td>
                                                                                                {formatConditions(
                                                                                                    weatherItem,
                                                                                                )}
                                                                                            </td>
                                                                                        )}
                                                                                        {displayWeatherField(
                                                                                            'Swell',
                                                                                        ) &&
                                                                                            allWeatherData.some(
                                                                                                (
                                                                                                    item,
                                                                                                ) =>
                                                                                                    item?.swell,
                                                                                            ) && (
                                                                                                <td>
                                                                                                    {weatherItem?.swell ||
                                                                                                        '-'}
                                                                                                </td>
                                                                                            )}
                                                                                        {displayWeatherField(
                                                                                            'Pressure',
                                                                                        ) &&
                                                                                            allWeatherData.some(
                                                                                                (
                                                                                                    item,
                                                                                                ) =>
                                                                                                    item?.pressure,
                                                                                            ) && (
                                                                                                <td>
                                                                                                    {weatherItem?.pressure
                                                                                                        ? `${weatherItem.pressure} hPa`
                                                                                                        : '-'}
                                                                                                </td>
                                                                                            )}
                                                                                    </tr>
                                                                                    {displayWeatherField(
                                                                                        'Comment',
                                                                                    ) &&
                                                                                        weatherItem?.comment && (
                                                                                            <tr>
                                                                                                {displayWeatherField(
                                                                                                    'Comment',
                                                                                                ) &&
                                                                                                    allWeatherData.some(
                                                                                                        (
                                                                                                            item,
                                                                                                        ) =>
                                                                                                            item?.comment,
                                                                                                    ) && (
                                                                                                        <th>
                                                                                                            Comment
                                                                                                        </th>
                                                                                                    )}
                                                                                                <td
                                                                                                    colSpan={
                                                                                                        4
                                                                                                    }
                                                                                                    title={
                                                                                                        weatherItem?.comment
                                                                                                    }>
                                                                                                    {
                                                                                                        weatherItem?.comment
                                                                                                    }
                                                                                                </td>
                                                                                            </tr>
                                                                                        )}
                                                                                </>
                                                                            )
                                                                        },
                                                                    )}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                )}
                                        </>
                                    )
                                )
                            })()}

                            {vesselDailyCheck && dailyCheckCombined && (
                                <div id="daily_checks">
                                    <div className="mt-8 mb-4">
                                        <h5 className=" ">{`Daily Check`}</h5>
                                    </div>

                                    <div className="border border-outer-space-400 rounded-lg overflow-hidden">
                                        <table className={tableClass}>
                                            <thead>
                                                <tr>
                                                    <th> Section </th>
                                                    <th> Fields </th>
                                                    <th> Status </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {crewWelfare &&
                                                    crewWelfare.length > 0 && (
                                                        <>
                                                            <tr>
                                                                <td className="text-nowrap">
                                                                    Crew welfare
                                                                </td>
                                                                <td colSpan={2}>
                                                                    <table
                                                                        className={cn(
                                                                            tableClass,
                                                                            '[&_td:first-child]:!px-0',
                                                                        )}>
                                                                        <tbody>
                                                                            {crewWelfareCheckOk()
                                                                                ?.props
                                                                                ?.children
                                                                                ?.length >
                                                                                0 && (
                                                                                <>
                                                                                    <tr>
                                                                                        <td>
                                                                                            {crewWelfareCheckOk()}
                                                                                        </td>
                                                                                        <td>
                                                                                            Ok
                                                                                        </td>
                                                                                    </tr>
                                                                                    {getCrewWelfareFields(
                                                                                        crewWelfare[0],
                                                                                    )
                                                                                        ?.filter(
                                                                                            (
                                                                                                item: any,
                                                                                            ) =>
                                                                                                item.checked ===
                                                                                                    'Ok' &&
                                                                                                getCWComment(
                                                                                                    item.name,
                                                                                                    'FieldComment',
                                                                                                ),
                                                                                        )
                                                                                        .map(
                                                                                            (
                                                                                                item: any,
                                                                                            ) => (
                                                                                                <tr
                                                                                                    key={
                                                                                                        item.id
                                                                                                    }>
                                                                                                    <td>
                                                                                                        {`${item.label}: ${getCWComment(item.name)?.comment}`}
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        Ok
                                                                                                    </td>
                                                                                                </tr>
                                                                                            ),
                                                                                        )}
                                                                                </>
                                                                            )}
                                                                            {crewWelfareCheckNotOk()
                                                                                ?.props
                                                                                ?.children
                                                                                ?.length >
                                                                                0 && (
                                                                                <>
                                                                                    <tr>
                                                                                        <td>
                                                                                            {crewWelfareCheckNotOk()}
                                                                                        </td>
                                                                                        <td>
                                                                                            Not
                                                                                            Ok
                                                                                        </td>
                                                                                    </tr>
                                                                                    {getCrewWelfareFields(
                                                                                        crewWelfare[0],
                                                                                    )
                                                                                        ?.filter(
                                                                                            (
                                                                                                item: any,
                                                                                            ) =>
                                                                                                item.checked !==
                                                                                                    'Ok' &&
                                                                                                getCWComment(
                                                                                                    item.name,
                                                                                                    'FieldComment',
                                                                                                ),
                                                                                        )
                                                                                        .map(
                                                                                            (
                                                                                                item: any,
                                                                                            ) => (
                                                                                                <tr
                                                                                                    key={
                                                                                                        item.id
                                                                                                    }>
                                                                                                    <td>
                                                                                                        {`${item.label}: ${getCWComment(item.name)?.comment}`}
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        Not
                                                                                                        Ok
                                                                                                    </td>
                                                                                                </tr>
                                                                                            ),
                                                                                        )}
                                                                                </>
                                                                            )}
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </>
                                                    )}
                                                {dailyCheckTypes
                                                    .filter(
                                                        (type) =>
                                                            dailyCheckCombined?.filter(
                                                                (item: any) =>
                                                                    item.fieldSet ===
                                                                    type.title,
                                                            ).length > 0,
                                                    )
                                                    .map((type) => (
                                                        <React.Fragment
                                                            key={`${type.title}-section`}>
                                                            <tr
                                                                key={`${type.title}-1`}>
                                                                <td className="text-nowrap">
                                                                    <div>
                                                                        {getDailyCheckComment(
                                                                            type.value,
                                                                            'Section',
                                                                        ) ? (
                                                                            <>
                                                                                {
                                                                                    type.title
                                                                                }
                                                                                <br />
                                                                                <br />
                                                                                Comment:{' '}
                                                                                {
                                                                                    getDailyCheckComment(
                                                                                        type.value,
                                                                                        'Section',
                                                                                    )
                                                                                        ?.comment
                                                                                }
                                                                            </>
                                                                        ) : (
                                                                            type.title
                                                                        )}
                                                                    </div>
                                                                </td>
                                                                <td colSpan={2}>
                                                                    <table
                                                                        className={cn(
                                                                            tableClass,
                                                                            '[&_td:first-child]:!px-0',
                                                                        )}>
                                                                        <tbody>
                                                                            {dailyCheckOk(
                                                                                type,
                                                                            )?.props?.children?.filter(
                                                                                (
                                                                                    item: any,
                                                                                ) =>
                                                                                    item !==
                                                                                    '',
                                                                            )
                                                                                ?.length >
                                                                                0 && (
                                                                                <tr>
                                                                                    <td>
                                                                                        {dailyCheckOk(
                                                                                            type,
                                                                                        )}
                                                                                        {dailyCheckOk(
                                                                                            type,
                                                                                        )}
                                                                                    </td>
                                                                                    <td>
                                                                                        Ok
                                                                                    </td>
                                                                                </tr>
                                                                            )}
                                                                            {dailyCheckCombined
                                                                                ?.filter(
                                                                                    (
                                                                                        item: any,
                                                                                    ) =>
                                                                                        item.fieldSet ===
                                                                                            type.title &&
                                                                                        item.value ===
                                                                                            'Ok' &&
                                                                                        getDailyCheckComment(
                                                                                            item.fieldName,
                                                                                        ),
                                                                                )
                                                                                .map(
                                                                                    (
                                                                                        item: any,
                                                                                    ) => (
                                                                                        <tr
                                                                                            key={
                                                                                                item.id
                                                                                            }>
                                                                                            <td>
                                                                                                {`${getFieldLabel(item.fieldName, logBookConfig)}: ${getDailyCheckComment(item.fieldName)?.comment}`}
                                                                                            </td>
                                                                                            <td>
                                                                                                Ok
                                                                                            </td>
                                                                                        </tr>
                                                                                    ),
                                                                                )}
                                                                            {dailyCheckNotOk(
                                                                                type,
                                                                            )?.props?.children[0]?.filter(
                                                                                (
                                                                                    item: any,
                                                                                ) =>
                                                                                    item !==
                                                                                    '',
                                                                            )
                                                                                ?.length >
                                                                                0 ||
                                                                                (dailyCheckNotOk(
                                                                                    type,
                                                                                )
                                                                                    ?.props
                                                                                    ?.children[1]
                                                                                    ?.length >
                                                                                    0 && (
                                                                                    <tr
                                                                                        key={
                                                                                            type.title
                                                                                        }>
                                                                                        <td>
                                                                                            {dailyCheckNotOk(
                                                                                                type,
                                                                                            )}
                                                                                        </td>
                                                                                        <td>
                                                                                            {`Not Ok`}
                                                                                        </td>
                                                                                    </tr>
                                                                                ))}
                                                                            {type.value ===
                                                                            'Hull' ? (
                                                                                <>
                                                                                    {getHull(
                                                                                        logBookConfig,
                                                                                        vesselDailyCheck,
                                                                                    )[0]?.group?.map(
                                                                                        (
                                                                                            items: any,
                                                                                        ) =>
                                                                                            getFilteredNotItems(
                                                                                                items,
                                                                                                dailyCheckCombined,
                                                                                            )
                                                                                                .filter(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) =>
                                                                                                        getDailyCheckComment(
                                                                                                            item.name,
                                                                                                            'FieldComment',
                                                                                                        ),
                                                                                                )
                                                                                                .map(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) => (
                                                                                                        <tr
                                                                                                            key={
                                                                                                                item.id
                                                                                                            }>
                                                                                                            <td>
                                                                                                                {`${item.label}: ${getDailyCheckComment(item.name)?.comment}`}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                {`Not Ok`}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                    ),
                                                                                                ),
                                                                                    )}
                                                                                </>
                                                                            ) : type.value ===
                                                                              'Engine' ? (
                                                                                <>
                                                                                    {getEngine(
                                                                                        logBookConfig,
                                                                                        vesselDailyCheck,
                                                                                    )[0]?.group?.map(
                                                                                        (
                                                                                            items: any,
                                                                                        ) =>
                                                                                            getFilteredNotItems(
                                                                                                items,
                                                                                                dailyCheckCombined,
                                                                                            )
                                                                                                .filter(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) =>
                                                                                                        getDailyCheckComment(
                                                                                                            item.name,
                                                                                                            'FieldComment',
                                                                                                        ),
                                                                                                )
                                                                                                .map(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) => (
                                                                                                        <tr
                                                                                                            key={
                                                                                                                item.id
                                                                                                            }>
                                                                                                            <td>
                                                                                                                {`${item.label}: ${getDailyCheckComment(item.name)?.comment}`}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                {`Not Ok`}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                    ),
                                                                                                ),
                                                                                    )}
                                                                                </>
                                                                            ) : type.value ===
                                                                              'Safety' ? (
                                                                                <>
                                                                                    {getSafety(
                                                                                        logBookConfig,
                                                                                        vesselDailyCheck,
                                                                                    )[0]?.group?.map(
                                                                                        (
                                                                                            items: any,
                                                                                        ) =>
                                                                                            getFilteredNotItems(
                                                                                                items,
                                                                                                dailyCheckCombined,
                                                                                            )
                                                                                                .filter(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) =>
                                                                                                        getDailyCheckComment(
                                                                                                            item.name,
                                                                                                            'FieldComment',
                                                                                                        ),
                                                                                                )
                                                                                                .map(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) => (
                                                                                                        <tr
                                                                                                            key={
                                                                                                                item.id
                                                                                                            }>
                                                                                                            <td>
                                                                                                                {`${item.label}: ${getDailyCheckComment(item.name)?.comment}`}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                {`Not Ok`}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                    ),
                                                                                                ),
                                                                                    )}
                                                                                </>
                                                                            ) : type.value ===
                                                                              'Navigation' ? (
                                                                                <>
                                                                                    {getNavigation(
                                                                                        logBookConfig,
                                                                                        vesselDailyCheck,
                                                                                    )[0]?.group?.map(
                                                                                        (
                                                                                            items: any,
                                                                                        ) =>
                                                                                            getFilteredNotItems(
                                                                                                items,
                                                                                                dailyCheckCombined,
                                                                                            )
                                                                                                .filter(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) =>
                                                                                                        getDailyCheckComment(
                                                                                                            item.name,
                                                                                                            'FieldComment',
                                                                                                        ),
                                                                                                )
                                                                                                .map(
                                                                                                    (
                                                                                                        item: any,
                                                                                                    ) => (
                                                                                                        <tr
                                                                                                            key={
                                                                                                                item.id
                                                                                                            }>
                                                                                                            <td>
                                                                                                                {`${item.label}: ${getDailyCheckComment(item.name)?.comment}`}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                {`Not Ok`}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                    ),
                                                                                                ),
                                                                                    )}
                                                                                </>
                                                                            ) : (
                                                                                dailyCheckCombined
                                                                                    ?.filter(
                                                                                        (
                                                                                            item: any,
                                                                                        ) =>
                                                                                            item.fieldSet ===
                                                                                                type.title &&
                                                                                            item.value ===
                                                                                                'Not_Ok' &&
                                                                                            getDailyCheckComment(
                                                                                                item.fieldName,
                                                                                            ),
                                                                                    )
                                                                                    .map(
                                                                                        (
                                                                                            item: any,
                                                                                        ) => (
                                                                                            <tr
                                                                                                key={
                                                                                                    item.id
                                                                                                }>
                                                                                                <td>
                                                                                                    {`${getFieldLabel(item.fieldName, logBookConfig)}: ${getDailyCheckComment(item.fieldName)?.comment}`}
                                                                                                </td>
                                                                                                <td>
                                                                                                    {`Not Ok`}
                                                                                                </td>
                                                                                            </tr>
                                                                                        ),
                                                                                    )
                                                                            )}
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </React.Fragment>
                                                    ))}
                                                {signOffEmbeddedFields &&
                                                    signOffEmbeddedFields.length >
                                                        0 && (
                                                        <>
                                                            <tr>
                                                                <td>
                                                                    <div>
                                                                        Sign off
                                                                    </div>
                                                                </td>
                                                                <td colSpan={2}>
                                                                    <table
                                                                        className={cn(
                                                                            tableClass,
                                                                            '[&_td:first-child]:!px-0',
                                                                        )}>
                                                                        <tbody>
                                                                            {signoffCheckOk()
                                                                                ?.props
                                                                                ?.children
                                                                                ?.length >
                                                                                0 && (
                                                                                <tr>
                                                                                    <td>
                                                                                        {signoffCheckOk()}
                                                                                    </td>
                                                                                    <td>
                                                                                        Ok
                                                                                    </td>
                                                                                </tr>
                                                                            )}
                                                                            {signoffCheckNotOk()
                                                                                ?.props
                                                                                ?.children
                                                                                ?.length >
                                                                                0 && (
                                                                                <tr>
                                                                                    <td>
                                                                                        {signoffCheckNotOk()}
                                                                                    </td>
                                                                                    <td>
                                                                                        Not
                                                                                        Ok
                                                                                    </td>
                                                                                </tr>
                                                                            )}
                                                                            {/* {signOffEmbeddedFields.filter((item: any) => item.value === 'Ok' && !getDailyCheckComment(item.fieldName, 'FieldComment')).length > 0 && (
                                                                                        <tr >
                                                                                            <td >
                                                                                                {signOffEmbeddedFields.filter((item: any) => item.value === 'Ok' && !getDailyCheckComment(item.fieldName, 'FieldComment'))
                                                                                                    .map((item: any) => getFieldLabel(item.fieldName, logBookConfig, 'LogBookSignOff_LogBookComponent')).join(', ')}
                                                                                            </td>
                                                                                            <td>
                                                                                                Ok
                                                                                            </td>
                                                                                        </tr>
                                                                                    )}
                                                                                    {signOffEmbeddedFields.filter((item: any) => item.value === 'Ok' && getDailyCheckComment(item.fieldName, 'FieldComment')).length > 0 && (
                                                                                        <>
                                                                                            {signOffEmbeddedFields.filter((item: any) => item.value === 'Ok' && getDailyCheckComment(item.fieldName, 'FieldComment'))
                                                                                                .map((item: any) => (
                                                                                                    <tr key={`${item.fieldName}-ok-comment`} >
                                                                                                        <td >
                                                                                                            {`${getFieldLabel(item.fieldName, logBookConfig, 'LogBookSignOff_LogBookComponent')}: ${getDailyCheckComment(item.fieldName)?.comment}`}
                                                                                                        </td>
                                                                                                        <td> Ok </td>
                                                                                                    </tr>
                                                                                                ))}
                                                                                        </>
                                                                                    )}
                                                                                    {signOffEmbeddedFields.filter((item: any) => item.value === 'Not_Ok' && !getDailyCheckComment(item.fieldName, 'FieldComment')).length > 0 && (
                                                                                        <tr >
                                                                                            <td >
                                                                                                {signOffEmbeddedFields.filter((item: any) => item.value === 'Not_Ok' && !getDailyCheckComment(item.fieldName, 'FieldComment'))
                                                                                                    .map((item: any) => getFieldLabel(item.fieldName, logBookConfig, 'LogBookSignOff_LogBookComponent')).join(', ')}
                                                                                            </td>
                                                                                            <td>
                                                                                                Not
                                                                                                Ok
                                                                                            </td>
                                                                                        </tr>
                                                                                    )}
                                                                                    {signOffEmbeddedFields.filter((item: any) => item.value === 'Not_Ok' && getDailyCheckComment(item.fieldName, 'FieldComment')).length > 0 && (
                                                                                        <>
                                                                                            {signOffEmbeddedFields.filter((item: any) => item.value === 'Not_Ok' && getDailyCheckComment(item.fieldName, 'FieldComment')).map((item: any) => (
                                                                                                <tr key={`${item.fieldName}-not-ok-comment`} >
                                                                                                    <td >
                                                                                                        {`${getFieldLabel(item.fieldName, logBookConfig, 'LogBookSignOff_LogBookComponent')}: ${getDailyCheckComment(item.fieldName)?.comment}`}
                                                                                                    </td>
                                                                                                    <td> Not Ok </td>
                                                                                                </tr>
                                                                                            ))}
                                                                                        </>
                                                                                    )} */}
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </>
                                                    )}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div>
                            {tripReport && (
                                <div className="mt-8 mb-4">
                                    <h5 className=" ">Trip report</h5>
                                </div>
                            )}
                            {tripReport &&
                                tripReport.map((trip: any) => (
                                    <div
                                        key={trip.id}
                                        className="border border-outer-space-400 rounded-lg overflow-hidden mb-4">
                                        <table className={tableClass}>
                                            <tbody>
                                                <tr>
                                                    <th>Departure:</th>
                                                    <td>{trip?.departTime}</td>
                                                    <th>Depart location</th>
                                                    <td>
                                                        {trip?.fromLocation
                                                            ?.id > 0
                                                            ? trip?.fromLocation
                                                                  ?.title
                                                            : trip?.fromLocation
                                                                  ?.lat +
                                                              ' ' +
                                                              trip?.fromLocation
                                                                  ?.long}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>P.O.B</th>
                                                    <td>{getTripPOB(trip)}</td>
                                                    <th>Vehicles on board</th>
                                                    <td>
                                                        {
                                                            trip?.totalVehiclesCarried
                                                        }
                                                    </td>
                                                </tr>
                                                {trip?.designatedDangerousGoodsSailing &&
                                                    displayReportField(
                                                        'DesignatedDangerousGoodsSailing',
                                                    ) && (
                                                        <tr>
                                                            <th colSpan={4}>
                                                                This is a
                                                                designated
                                                                dangerous goods
                                                                sailing
                                                            </th>
                                                        </tr>
                                                    )}
                                                {trip?.dangerousGoodsRecords
                                                    ?.nodes?.length > 0 && (
                                                    <tr>
                                                        <th>Dangerous goods</th>
                                                        <td colSpan={3}>
                                                            {trip?.dangerousGoodsRecords?.nodes?.map(
                                                                (
                                                                    item: any,
                                                                    index: number,
                                                                ) => (
                                                                    <React.Fragment
                                                                        key={
                                                                            item.id
                                                                        }>
                                                                        {index >
                                                                            0 &&
                                                                            ', '}
                                                                        {
                                                                            goodsTypes.find(
                                                                                (
                                                                                    type,
                                                                                ) =>
                                                                                    type.value ===
                                                                                    item?.type,
                                                                            )
                                                                                ?.label
                                                                        }
                                                                        {' - '}
                                                                        <div
                                                                            className="inline-block"
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: item?.comment,
                                                                            }}></div>
                                                                    </React.Fragment>
                                                                ),
                                                            )}
                                                        </td>
                                                    </tr>
                                                )}
                                                {displayEventTypeField(
                                                    'PassengerVehiclePickDrop',
                                                ) &&
                                                    trip
                                                        ?.dangerousGoodsChecklist
                                                        ?.id > 0 && (
                                                        <tr
                                                            key={
                                                                trip
                                                                    .dangerousGoodsChecklist
                                                                    .id
                                                            }>
                                                            <td colSpan={4}>
                                                                <table
                                                                    className={cn(
                                                                        tableClass,
                                                                        '[&_td:first-child]:!px-0',
                                                                    )}>
                                                                    <tbody
                                                                        className={cn(
                                                                            tableClass,
                                                                            '[&_td:first-child]:!px-0',
                                                                        )}>
                                                                        <tr>
                                                                            <td>
                                                                                Dangerous
                                                                                goods
                                                                                checklist
                                                                            </td>
                                                                            <td>
                                                                                <table
                                                                                    className={cn(
                                                                                        tableClass,
                                                                                        '[&_td:first-child]:!px-0',
                                                                                    )}>
                                                                                    <tbody>
                                                                                        {Object.entries(
                                                                                            trip.dangerousGoodsChecklist,
                                                                                        ).filter(
                                                                                            ([
                                                                                                key,
                                                                                                value,
                                                                                            ]) =>
                                                                                                value ===
                                                                                                true,
                                                                                        )
                                                                                            .length >
                                                                                            0 && (
                                                                                            <tr>
                                                                                                <td>
                                                                                                    {Object.entries(
                                                                                                        trip.dangerousGoodsChecklist,
                                                                                                    )
                                                                                                        .filter(
                                                                                                            ([
                                                                                                                key,
                                                                                                                value,
                                                                                                            ]) =>
                                                                                                                value ===
                                                                                                                true,
                                                                                                        )
                                                                                                        .map(
                                                                                                            ([
                                                                                                                key,
                                                                                                                value,
                                                                                                            ]) => ({
                                                                                                                key,
                                                                                                                value,
                                                                                                            }),
                                                                                                        )
                                                                                                        .map(
                                                                                                            (
                                                                                                                item,
                                                                                                            ) => {
                                                                                                                return item.key
                                                                                                                    .replace(
                                                                                                                        /([A-Z])/g,
                                                                                                                        ' $1',
                                                                                                                    )
                                                                                                                    .replace(
                                                                                                                        /^./,
                                                                                                                        (
                                                                                                                            str,
                                                                                                                        ) =>
                                                                                                                            str.toUpperCase(),
                                                                                                                    )
                                                                                                            },
                                                                                                        )
                                                                                                        .join(
                                                                                                            ', ',
                                                                                                        )}
                                                                                                </td>
                                                                                                <td>
                                                                                                    Ok
                                                                                                </td>
                                                                                            </tr>
                                                                                        )}
                                                                                        {Object.entries(
                                                                                            trip.dangerousGoodsChecklist,
                                                                                        ).filter(
                                                                                            ([
                                                                                                key,
                                                                                                value,
                                                                                            ]) =>
                                                                                                value ===
                                                                                                false,
                                                                                        )
                                                                                            .length >
                                                                                            0 && (
                                                                                            <tr>
                                                                                                <td>
                                                                                                    {Object.entries(
                                                                                                        trip.dangerousGoodsChecklist,
                                                                                                    )
                                                                                                        .filter(
                                                                                                            ([
                                                                                                                key,
                                                                                                                value,
                                                                                                            ]) =>
                                                                                                                value ===
                                                                                                                false,
                                                                                                        )
                                                                                                        .map(
                                                                                                            ([
                                                                                                                key,
                                                                                                                value,
                                                                                                            ]) => ({
                                                                                                                key,
                                                                                                                value,
                                                                                                            }),
                                                                                                        )
                                                                                                        .map(
                                                                                                            (
                                                                                                                item,
                                                                                                            ) => {
                                                                                                                return item.key
                                                                                                                    .replace(
                                                                                                                        /([A-Z])/g,
                                                                                                                        ' $1',
                                                                                                                    )
                                                                                                                    .replace(
                                                                                                                        /^./,
                                                                                                                        (
                                                                                                                            str,
                                                                                                                        ) =>
                                                                                                                            str.toUpperCase(),
                                                                                                                    )
                                                                                                            },
                                                                                                        )
                                                                                                        .join(
                                                                                                            ', ',
                                                                                                        )}
                                                                                                </td>
                                                                                                <td>
                                                                                                    Not
                                                                                                    Ok
                                                                                                </td>
                                                                                            </tr>
                                                                                        )}
                                                                                        {trip
                                                                                            ?.dangerousGoodsChecklist
                                                                                            ?.riskFactors
                                                                                            ?.nodes
                                                                                            ?.length >
                                                                                            0 && (
                                                                                            <tr>
                                                                                                <td
                                                                                                    colSpan={
                                                                                                        2
                                                                                                    }>
                                                                                                    {trip?.dangerousGoodsChecklist.riskFactors.nodes.map(
                                                                                                        (
                                                                                                            item: any,
                                                                                                            index: number,
                                                                                                        ) => (
                                                                                                            <div
                                                                                                                className="inline-block"
                                                                                                                key={
                                                                                                                    item.id
                                                                                                                }>
                                                                                                                {index >
                                                                                                                0
                                                                                                                    ? ', '
                                                                                                                    : 'Risk factors: '}
                                                                                                                {`${item.title} - ${item.impact} - ${item.probability}/10`}
                                                                                                            </div>
                                                                                                        ),
                                                                                                    )}
                                                                                                </td>
                                                                                            </tr>
                                                                                        )}
                                                                                        {trip
                                                                                            ?.dangerousGoodsChecklist
                                                                                            ?.member
                                                                                            ?.id >
                                                                                            0 && (
                                                                                            <tr>
                                                                                                <td
                                                                                                    colSpan={
                                                                                                        2
                                                                                                    }>
                                                                                                    {`Author: ${trip.dangerousGoodsChecklist.member?.firstName} ${trip.dangerousGoodsChecklist.member?.surname}`}
                                                                                                </td>
                                                                                            </tr>
                                                                                        )}
                                                                                    </tbody>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    )}
                                                {trip?.tripReport_Stops?.nodes
                                                    ?.length > 0 && (
                                                    <>
                                                        {trip?.tripReport_Stops?.nodes?.map(
                                                            (stop: any) => (
                                                                <tr
                                                                    key={
                                                                        stop.id
                                                                    }>
                                                                    <td
                                                                        colSpan={
                                                                            2
                                                                        }>
                                                                        <table
                                                                            className={cn(
                                                                                tableClass,
                                                                                '[&_td:first-child]:!px-0',
                                                                            )}>
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td>
                                                                                        <span className="w-48 inline-block">
                                                                                            Trip
                                                                                            stop
                                                                                        </span>
                                                                                    </td>
                                                                                    <td>
                                                                                        <table
                                                                                            className={cn(
                                                                                                tableClass,
                                                                                                '[&_td:first-child]:!px-0',
                                                                                            )}>
                                                                                            <tbody>
                                                                                                <tr
                                                                                                    key={`${stop.id}-stop-location`}>
                                                                                                    <td>
                                                                                                        <span className="w-48 inline-block">
                                                                                                            {`Location`}
                                                                                                        </span>
                                                                                                        {`${stop?.stopLocation?.id > 0 ? stop?.stopLocation?.title : ''}`}
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        {stop?.designatedDangerousGoodsSailing &&
                                                                                                        displayReportField(
                                                                                                            'DesignatedDangerousGoodsSailing',
                                                                                                        )
                                                                                                            ? 'This is a designated dangerous goods sailing'
                                                                                                            : '-'}
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr
                                                                                                    key={`${stop.id}-arr-time`}>
                                                                                                    <td>
                                                                                                        <span className="w-48 inline-block">
                                                                                                            {`Arrival`}
                                                                                                        </span>
                                                                                                        {`${stop?.arriveTime}`}
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <span className="w-48 inline-block">
                                                                                                            {`Departure`}
                                                                                                        </span>
                                                                                                        {`${stop?.departTime}`}
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr
                                                                                                    key={`${stop.id}-pax`}>
                                                                                                    <td>
                                                                                                        <span className="w-48 inline-block">
                                                                                                            {`Pax joined`}
                                                                                                        </span>
                                                                                                        {`${stop?.paxJoined}`}
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <span className="w-48 inline-block">
                                                                                                            {`Pax departed`}
                                                                                                        </span>
                                                                                                        {`${stop?.paxDeparted}`}
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr
                                                                                                    key={`${stop.id}-vehicles`}>
                                                                                                    <td>
                                                                                                        <span className="w-48 inline-block">
                                                                                                            {`Vehicles joined`}
                                                                                                        </span>
                                                                                                        {`${stop?.vehiclesJoined}`}
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <span className="w-48 inline-block">
                                                                                                            {`Vehicles departed`}
                                                                                                        </span>
                                                                                                        {`${stop?.vehiclesDeparted}`}
                                                                                                    </td>
                                                                                                </tr>
                                                                                                {stop?.otherCargo && (
                                                                                                    <tr
                                                                                                        key={`${stop.id}-otherCargo`}>
                                                                                                        <td
                                                                                                            colSpan={
                                                                                                                2
                                                                                                            }>
                                                                                                            <span className="w-48 inline-block">
                                                                                                                {`Other cargo: `}
                                                                                                            </span>
                                                                                                            {`${stop?.otherCargo}`}
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                )}
                                                                                                {stop?.comments && (
                                                                                                    <tr
                                                                                                        key={`${stop.id}-comments`}>
                                                                                                        <td
                                                                                                            colSpan={
                                                                                                                2
                                                                                                            }>
                                                                                                            <span className="w-48 inline-block">
                                                                                                                {`Comments: `}
                                                                                                            </span>
                                                                                                            {`${stop?.comments}`}
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                )}
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            ),
                                                        )}
                                                    </>
                                                )}
                                                {trip?.tripEvents?.nodes
                                                    ?.length > 0 && (
                                                    <>
                                                        <tr>
                                                            <td colSpan={2}>
                                                                <table
                                                                    className={cn(
                                                                        tableClass,
                                                                        '[&_td:first-child]:!px-0',
                                                                    )}>
                                                                    <tbody>
                                                                        {trip?.tripEvents?.nodes?.map(
                                                                            (
                                                                                event: any,
                                                                            ) => (
                                                                                <>
                                                                                    {event.eventCategory ===
                                                                                        'BarCrossing' && (
                                                                                        <tr
                                                                                            key={
                                                                                                event.id
                                                                                            }>
                                                                                            <td>
                                                                                                <span className="w-48 inline-block">
                                                                                                    Activity
                                                                                                    -
                                                                                                    Bar
                                                                                                    crossing
                                                                                                </span>
                                                                                            </td>
                                                                                            <td>
                                                                                                <table
                                                                                                    className={
                                                                                                        tableClass
                                                                                                    }>
                                                                                                    <tbody>
                                                                                                        <tr
                                                                                                            key={`${event.eventType_BarCrossing.id}-bc-type`}>
                                                                                                            <td>
                                                                                                                <span className="w-48 inline-block">
                                                                                                                    {`Event Type`}
                                                                                                                </span>
                                                                                                                {`Bar crossing`}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <span className="w-48 inline-block">
                                                                                                                    {`Location`}
                                                                                                                </span>
                                                                                                                {`${event?.eventType_BarCrossing?.geoLocation?.id > 0 ? event?.eventType_BarCrossing?.geoLocation?.title : ''}`}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr
                                                                                                            key={`${event.id}-crossing-time`}>
                                                                                                            <td>
                                                                                                                <span className="w-48 inline-block">
                                                                                                                    {`Crossing start`}
                                                                                                                </span>
                                                                                                                {`${event?.eventType_BarCrossing.time}`}
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <span className="w-48 inline-block">
                                                                                                                    {`Crossing end`}
                                                                                                                </span>
                                                                                                                {`${event?.eventType_BarCrossing.timeCompleted}`}
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        {event
                                                                                                            ?.eventType_BarCrossing
                                                                                                            ?.barCrossingChecklist
                                                                                                            ?.id >
                                                                                                            0 && (
                                                                                                            <tr
                                                                                                                key={`${event.id}-bc-checklist`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <table
                                                                                                                        className={
                                                                                                                            tableClass
                                                                                                                        }>
                                                                                                                        <tbody>
                                                                                                                            <tr>
                                                                                                                                <td>
                                                                                                                                    <span className="w-48 inline-block">
                                                                                                                                        {`Bar crossing checklist`}
                                                                                                                                    </span>
                                                                                                                                </td>
                                                                                                                                <td className="p-0">
                                                                                                                                    <table
                                                                                                                                        className={cn(
                                                                                                                                            tableClass,
                                                                                                                                            '[&_td:first-child]:!px-0',
                                                                                                                                        )}>
                                                                                                                                        <tbody>
                                                                                                                                            {Object.entries(
                                                                                                                                                event
                                                                                                                                                    ?.eventType_BarCrossing
                                                                                                                                                    ?.barCrossingChecklist,
                                                                                                                                            ).filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    true,
                                                                                                                                            )
                                                                                                                                                .length >
                                                                                                                                                0 && (
                                                                                                                                                <tr
                                                                                                                                                    key={`${event.id}-bc-checklist-y`}>
                                                                                                                                                    <td>
                                                                                                                                                        {Object.entries(
                                                                                                                                                            event
                                                                                                                                                                ?.eventType_BarCrossing
                                                                                                                                                                ?.barCrossingChecklist,
                                                                                                                                                        )
                                                                                                                                                            .filter(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) =>
                                                                                                                                                                    value ===
                                                                                                                                                                    true,
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) => {
                                                                                                                                                                    return key
                                                                                                                                                                        .replace(
                                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                                            ' $1',
                                                                                                                                                                        )
                                                                                                                                                                        .replace(
                                                                                                                                                                            /^./,
                                                                                                                                                                            (
                                                                                                                                                                                str,
                                                                                                                                                                            ) =>
                                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                                        )
                                                                                                                                                                },
                                                                                                                                                            )
                                                                                                                                                            .join(
                                                                                                                                                                ', ',
                                                                                                                                                            )}
                                                                                                                                                    </td>
                                                                                                                                                    <td>
                                                                                                                                                        Ok
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                            {Object.entries(
                                                                                                                                                event
                                                                                                                                                    ?.eventType_BarCrossing
                                                                                                                                                    ?.barCrossingChecklist,
                                                                                                                                            ).filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    false,
                                                                                                                                            )
                                                                                                                                                .length >
                                                                                                                                                0 && (
                                                                                                                                                <tr
                                                                                                                                                    key={`${event.id}-bc-checklist-n`}>
                                                                                                                                                    <td>
                                                                                                                                                        {Object.entries(
                                                                                                                                                            event
                                                                                                                                                                ?.eventType_BarCrossing
                                                                                                                                                                ?.barCrossingChecklist,
                                                                                                                                                        )
                                                                                                                                                            .filter(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) =>
                                                                                                                                                                    value ===
                                                                                                                                                                    false,
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) => {
                                                                                                                                                                    return key
                                                                                                                                                                        .replace(
                                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                                            ' $1',
                                                                                                                                                                        )
                                                                                                                                                                        .replace(
                                                                                                                                                                            /^./,
                                                                                                                                                                            (
                                                                                                                                                                                str,
                                                                                                                                                                            ) =>
                                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                                        )
                                                                                                                                                                },
                                                                                                                                                            )
                                                                                                                                                            .join(
                                                                                                                                                                ', ',
                                                                                                                                                            )}
                                                                                                                                                    </td>
                                                                                                                                                    <td>
                                                                                                                                                        Not
                                                                                                                                                        Ok
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                        </tbody>
                                                                                                                                    </table>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        </tbody>
                                                                                                                    </table>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                        {trip
                                                                                                            ?.eventType_BarCrossing
                                                                                                            ?.barCrossingChecklist
                                                                                                            ?.riskFactors
                                                                                                            ?.nodes
                                                                                                            ?.length >
                                                                                                            0 && (
                                                                                                            <tr>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    {trip?.eventType_BarCrossing?.barCrossingChecklist.riskFactors.nodes.map(
                                                                                                                        (
                                                                                                                            item: any,
                                                                                                                            index: number,
                                                                                                                        ) => (
                                                                                                                            <div
                                                                                                                                className="inline-block"
                                                                                                                                key={
                                                                                                                                    item.id
                                                                                                                                }>
                                                                                                                                {index >
                                                                                                                                0
                                                                                                                                    ? ', '
                                                                                                                                    : 'Risk factors: '}
                                                                                                                                {`${item.title} - ${item.impact} - ${item.probability}/10`}
                                                                                                                            </div>
                                                                                                                        ),
                                                                                                                    )}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        )}
                                                                                                    </tbody>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    )}
                                                                                    {event.eventCategory ===
                                                                                        'RestrictedVisibility' && (
                                                                                        <>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span className="w-48 inline-block">
                                                                                                        Activity
                                                                                                        -
                                                                                                        Restricted
                                                                                                        visibility
                                                                                                    </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <table
                                                                                                        className={cn(
                                                                                                            tableClass,
                                                                                                            '[&_td:first-child]:!px-0',
                                                                                                        )}>
                                                                                                        <tbody>
                                                                                                            <tr
                                                                                                                key={`${event.eventType_RestrictedVisibility.id}-rv-type`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Event Type`}
                                                                                                                    </span>
                                                                                                                    {`Restricted visibility`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.eventType_RestrictedVisibility.id}-locationrv`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Start location`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RestrictedVisibility?.startLocation?.id > 0 ? event?.eventType_RestrictedVisibility?.startLocation?.title : ''}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`End location`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RestrictedVisibility?.endLocation?.id > 0 ? event?.eventType_RestrictedVisibility?.endLocation?.title : ''}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.id}-locationbc`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Crossing Time`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RestrictedVisibility?.crossingTime}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Crossed time`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RestrictedVisibility?.crossedTime}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.id}-speed`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Safe speed`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RestrictedVisibility?.estSafeSpeed}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Avg speed`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RestrictedVisibility?.approxSafeSpeed}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.id}-rv-checklist`}>
                                                                                                                <td
                                                                                                                    colSpan={
                                                                                                                        2
                                                                                                                    }>
                                                                                                                    <table
                                                                                                                        className={
                                                                                                                            tableClass
                                                                                                                        }>
                                                                                                                        <tbody>
                                                                                                                            <tr>
                                                                                                                                <td>
                                                                                                                                    <span className="w-48 inline-block">
                                                                                                                                        {`Safe operating procedures checklist`}
                                                                                                                                    </span>
                                                                                                                                </td>
                                                                                                                                <td className="p-0">
                                                                                                                                    <table
                                                                                                                                        className={
                                                                                                                                            tableClass
                                                                                                                                        }>
                                                                                                                                        <tbody>
                                                                                                                                            {Object.entries(
                                                                                                                                                event?.eventType_RestrictedVisibility,
                                                                                                                                            ).filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    true,
                                                                                                                                            )
                                                                                                                                                .length >
                                                                                                                                                0 && (
                                                                                                                                                <tr
                                                                                                                                                    key={`${event.id}-rv-checklist-y`}>
                                                                                                                                                    <td>
                                                                                                                                                        {Object.entries(
                                                                                                                                                            event?.eventType_RestrictedVisibility,
                                                                                                                                                        )
                                                                                                                                                            .filter(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) =>
                                                                                                                                                                    value ===
                                                                                                                                                                    true,
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) => {
                                                                                                                                                                    return key
                                                                                                                                                                        .replace(
                                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                                            ' $1',
                                                                                                                                                                        )
                                                                                                                                                                        .replace(
                                                                                                                                                                            /^./,
                                                                                                                                                                            (
                                                                                                                                                                                str,
                                                                                                                                                                            ) =>
                                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                                        )
                                                                                                                                                                },
                                                                                                                                                            )
                                                                                                                                                            .join(
                                                                                                                                                                ', ',
                                                                                                                                                            )}
                                                                                                                                                    </td>
                                                                                                                                                    <td>
                                                                                                                                                        Ok
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                            {Object.entries(
                                                                                                                                                event?.eventType_RestrictedVisibility,
                                                                                                                                            ).filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    false,
                                                                                                                                            )
                                                                                                                                                .length >
                                                                                                                                                0 && (
                                                                                                                                                <tr
                                                                                                                                                    key={`${event.id}-rv-checklist-n`}>
                                                                                                                                                    <td>
                                                                                                                                                        {Object.entries(
                                                                                                                                                            event?.eventType_RestrictedVisibility,
                                                                                                                                                        )
                                                                                                                                                            .filter(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) =>
                                                                                                                                                                    value ===
                                                                                                                                                                    false,
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) => {
                                                                                                                                                                    return key
                                                                                                                                                                        .replace(
                                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                                            ' $1',
                                                                                                                                                                        )
                                                                                                                                                                        .replace(
                                                                                                                                                                            /^./,
                                                                                                                                                                            (
                                                                                                                                                                                str,
                                                                                                                                                                            ) =>
                                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                                        )
                                                                                                                                                                },
                                                                                                                                                            )
                                                                                                                                                            .join(
                                                                                                                                                                ', ',
                                                                                                                                                            )}
                                                                                                                                                    </td>
                                                                                                                                                    <td>
                                                                                                                                                        Not
                                                                                                                                                        Ok
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                        </tbody>
                                                                                                                                    </table>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        </tbody>
                                                                                                                    </table>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </>
                                                                                    )}
                                                                                    {event.eventCategory ===
                                                                                        'CrewTraining' && (
                                                                                        <>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span className="w-48 inline-block">
                                                                                                        Activity
                                                                                                        -
                                                                                                        Training
                                                                                                    </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <table
                                                                                                        className={cn(
                                                                                                            tableClass,
                                                                                                            '[&_td:first-child]:!px-0',
                                                                                                        )}>
                                                                                                        <tbody>
                                                                                                            <tr
                                                                                                                key={`${event.crewTraining.id}-ct-type`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Event type`}
                                                                                                                    </span>
                                                                                                                    {`Crew training`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Training date`}
                                                                                                                    </span>
                                                                                                                    {`${formatDate(event.crewTraining.date)}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.crewTraining.id}-locationbc`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Start time`}
                                                                                                                    </span>
                                                                                                                    {`${event?.crewTraining?.startTime}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`End time`}
                                                                                                                    </span>
                                                                                                                    {`${event?.crewTraining?.finishTime}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.crewTraining.id}-trainer`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Trainer`}
                                                                                                                    </span>
                                                                                                                    {`${event?.crewTraining?.trainer?.id > 0 ? event?.crewTraining?.trainer?.firstName + ' ' + event?.crewTraining?.trainer?.surname : ''}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Location`}
                                                                                                                    </span>
                                                                                                                    {`${event?.crewTraining?.geoLocation?.id > 0 ? event?.crewTraining?.geoLocation?.title : ''}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.crewTraining.id}-crew-trained`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Crew trained`}
                                                                                                                    </span>
                                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                                        {event?.crewTraining?.members?.nodes?.map(
                                                                                                                            (
                                                                                                                                member: any,
                                                                                                                                index: number,
                                                                                                                            ) => (
                                                                                                                                <React.Fragment
                                                                                                                                    key={
                                                                                                                                        member.id
                                                                                                                                    }>
                                                                                                                                    {index >
                                                                                                                                        0 &&
                                                                                                                                        ', '}
                                                                                                                                    {`${member.firstName} ${member.surname}`}
                                                                                                                                </React.Fragment>
                                                                                                                            ),
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Training type`}
                                                                                                                    </span>
                                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                                        {event?.crewTraining?.trainingTypes?.nodes?.map(
                                                                                                                            (
                                                                                                                                type: any,
                                                                                                                                index: number,
                                                                                                                            ) => (
                                                                                                                                <React.Fragment
                                                                                                                                    key={
                                                                                                                                        type.id
                                                                                                                                    }>
                                                                                                                                    {index >
                                                                                                                                        0 &&
                                                                                                                                        ', '}
                                                                                                                                    {`${type.title}`}
                                                                                                                                </React.Fragment>
                                                                                                                            ),
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            {event
                                                                                                                ?.crewTraining
                                                                                                                ?.trainingSummary && (
                                                                                                                <tr className=" border-b last:border-b-0    ">
                                                                                                                    <td
                                                                                                                        colSpan={
                                                                                                                            2
                                                                                                                        }>
                                                                                                                        <div className="flex">
                                                                                                                            <span className="min-w-48 inline-block">
                                                                                                                                Training
                                                                                                                                summary
                                                                                                                            </span>
                                                                                                                            <div className="inline-block">
                                                                                                                                <div
                                                                                                                                    dangerouslySetInnerHTML={{
                                                                                                                                        __html: event
                                                                                                                                            ?.crewTraining
                                                                                                                                            ?.trainingSummary,
                                                                                                                                    }}></div>
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                            )}
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </>
                                                                                    )}
                                                                                    {event.eventCategory ===
                                                                                        'Tasking' && (
                                                                                        <>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span className="w-48 inline-block">
                                                                                                        Activity
                                                                                                        -
                                                                                                        Tasking
                                                                                                    </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <table
                                                                                                        className={cn(
                                                                                                            tableClass,
                                                                                                            '[&_td:first-child]:!px-0',
                                                                                                        )}>
                                                                                                        <tbody>
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et-type`}>
                                                                                                                <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Event type`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_Tasking?.type.replace(/([A-Z])/g, ' $1')}`}
                                                                                                                </td>
                                                                                                                <td className="px-6 pb-4 w-1/2">
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Time`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_Tasking?.time}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et2-type`}>
                                                                                                                <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Title`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_Tasking?.title}`}
                                                                                                                </td>
                                                                                                                <td className="px-6 pb-4 w-1/2">
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Location`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_Tasking?.geoLocation?.id > 0 ? event?.eventType_Tasking?.geoLocation?.title : ''}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.eventType_Tasking.id}-et3-type`}>
                                                                                                                <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Activity type`}
                                                                                                                    </span>
                                                                                                                    {getActivityType(
                                                                                                                        event.eventType_Tasking,
                                                                                                                        trip,
                                                                                                                    )}
                                                                                                                </td>
                                                                                                                <td className="px-6 pb-4 w-1/2">
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Fuel`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_Tasking?.fuelLog?.nodes?.length ? event?.eventType_Tasking?.fuelLog?.nodes[0]?.fuelAfter : '-'}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            {event
                                                                                                                ?.eventType_Tasking
                                                                                                                ?.vesselRescueID >
                                                                                                                0 &&
                                                                                                                event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.type ===
                                                                                                                    'TaskingStartUnderway' && (
                                                                                                                    <>
                                                                                                                        <tr
                                                                                                                            key={`${event.eventType_Tasking.id}-et-vr1-type`}>
                                                                                                                            <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Target vessel`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.vesselName}`}
                                                                                                                            </td>
                                                                                                                            <td className="px-6 pb-4 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Call sign`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.callSign}`}
                                                                                                                            </td>
                                                                                                                        </tr>
                                                                                                                        <tr
                                                                                                                            key={`${event.eventType_Tasking.id}-et-vr2-type`}>
                                                                                                                            <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`P.O.B`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.pob}`}
                                                                                                                            </td>
                                                                                                                            <td className="px-6 pb-4 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Location`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.vesselLocation?.id > 0 ? event?.eventType_Tasking?.vesselRescue?.vesselLocation?.title : ''}`}
                                                                                                                            </td>
                                                                                                                        </tr>
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.vesselRescue
                                                                                                                            ?.locationDescription && (
                                                                                                                            <tr
                                                                                                                                key={`${event.eventType_Tasking.id}-et-vr3-type`}>
                                                                                                                                <td
                                                                                                                                    colSpan={
                                                                                                                                        2
                                                                                                                                    }>
                                                                                                                                    <span className="w-48 inline-block">
                                                                                                                                        {`Location description`}
                                                                                                                                    </span>
                                                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                                                        {`${event?.eventType_Tasking?.vesselRescue?.locationDescription}`}
                                                                                                                                    </div>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        )}
                                                                                                                        <tr
                                                                                                                            key={`${event.eventType_Tasking.id}-et-vr4-type`}>
                                                                                                                            <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Vessel length`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.vesselLength}`}
                                                                                                                            </td>
                                                                                                                            <td className="px-6 pb-4 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Vessel type`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.vesselType}`}
                                                                                                                            </td>
                                                                                                                        </tr>
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.vesselRescue
                                                                                                                            ?.vesselTypeDescription && (
                                                                                                                            <tr
                                                                                                                                key={`${event.eventType_Tasking.id}-et-vr5-type`}>
                                                                                                                                <td
                                                                                                                                    colSpan={
                                                                                                                                        2
                                                                                                                                    }>
                                                                                                                                    <span className="w-48 inline-block">
                                                                                                                                        {`Vessel description`}
                                                                                                                                    </span>
                                                                                                                                    <div className="inline-block max-w-[calc(100%-12rem)]">
                                                                                                                                        {`${event?.eventType_Tasking?.vesselRescue?.vesselTypeDescription}`}
                                                                                                                                    </div>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        )}
                                                                                                                        <tr
                                                                                                                            key={`${event.eventType_Tasking.id}-et-vr6-type`}>
                                                                                                                            <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Make and model`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.makeAndModel}`}
                                                                                                                            </td>
                                                                                                                            <td className="px-6 pb-4 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Vessel color`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.color}`}
                                                                                                                            </td>
                                                                                                                        </tr>
                                                                                                                        <tr
                                                                                                                            key={`${event.eventType_Tasking.id}-et-vr7-type`}>
                                                                                                                            <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Owner name`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.ownerName}`}
                                                                                                                            </td>
                                                                                                                            <td className="px-6 pb-4 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Owner on board?`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.ownerOnBoard ? 'Yes' : 'No'}`}
                                                                                                                            </td>
                                                                                                                        </tr>
                                                                                                                        <tr
                                                                                                                            key={`${event.eventType_Tasking.id}-et-vr8-type`}>
                                                                                                                            <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Owner phone`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.phone}`}
                                                                                                                            </td>
                                                                                                                            <td className="px-6 pb-4 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`CG Membership`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.cgMembership}`}
                                                                                                                            </td>
                                                                                                                        </tr>
                                                                                                                        <tr
                                                                                                                            key={`${event.eventType_Tasking.id}-et-vr9-type`}>
                                                                                                                            <td className="px-6 pb-4 border-r-1 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Owner email`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.email}`}
                                                                                                                            </td>
                                                                                                                            <td className="px-6 pb-4 w-1/2">
                                                                                                                                <span className="w-48 inline-block">
                                                                                                                                    {`Owner address`}
                                                                                                                                </span>
                                                                                                                                {`${event?.eventType_Tasking?.vesselRescue?.address}`}
                                                                                                                            </td>
                                                                                                                        </tr>
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.cgop && (
                                                                                                                            <tr
                                                                                                                                key={`${event.eventType_Tasking.id}-et-incident-cgop`}>
                                                                                                                                <td
                                                                                                                                    colSpan={
                                                                                                                                        2
                                                                                                                                    }>
                                                                                                                                    <span className="w-48 inline-block">
                                                                                                                                        {`Incident details`}
                                                                                                                                    </span>
                                                                                                                                    {`CoastGuard Rescue - ${event?.eventType_Tasking?.cgop}`}
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        )}
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.sarop && (
                                                                                                                            <tr
                                                                                                                                key={`${event.eventType_Tasking.id}-et-incident-sarop`}>
                                                                                                                                <td
                                                                                                                                    colSpan={
                                                                                                                                        2
                                                                                                                                    }>
                                                                                                                                    <span className="w-48 inline-block">
                                                                                                                                        {`Incident details`}
                                                                                                                                    </span>
                                                                                                                                    {`SAROP - ${event?.eventType_Tasking?.sarop}`}
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        )}
                                                                                                                    </>
                                                                                                                )}
                                                                                                            {event
                                                                                                                ?.eventType_Tasking
                                                                                                                ?.vesselRescueID >
                                                                                                                0 &&
                                                                                                                event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.type ===
                                                                                                                    'TaskingComplete' && (
                                                                                                                    <>
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.vesselRescue
                                                                                                                            ?.mission
                                                                                                                            ?.id >
                                                                                                                            0 && (
                                                                                                                            <tr
                                                                                                                                key={`${event.eventType_Tasking.id}-et-mission`}>
                                                                                                                                <td
                                                                                                                                    colSpan={
                                                                                                                                        2
                                                                                                                                    }>
                                                                                                                                    <span className="w-48 inline-block">
                                                                                                                                        {`Mission outcome`}
                                                                                                                                    </span>
                                                                                                                                    {`${event?.eventType_Tasking?.vesselRescue?.mission?.operationOutcome?.replace(/_/g, ' ')}`}
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        )}
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.vesselRescue
                                                                                                                            ?.missionTimeline
                                                                                                                            ?.nodes
                                                                                                                            ?.length >
                                                                                                                            0 && (
                                                                                                                            <tr
                                                                                                                                key={`${event.eventType_Tasking.id}-et-mission-timeline`}>
                                                                                                                                <td
                                                                                                                                    className="px-6"
                                                                                                                                    colSpan={
                                                                                                                                        2
                                                                                                                                    }>
                                                                                                                                    <div className="pb-4">
                                                                                                                                        {`Mission notes/comments`}
                                                                                                                                    </div>
                                                                                                                                    <table
                                                                                                                                        className={
                                                                                                                                            tableClass
                                                                                                                                        }>
                                                                                                                                        <tbody>
                                                                                                                                            {event?.eventType_Tasking?.vesselRescue?.missionTimeline?.nodes?.map(
                                                                                                                                                (
                                                                                                                                                    mission: any,
                                                                                                                                                ) => (
                                                                                                                                                    <tr
                                                                                                                                                        key={`${mission.id}-et-mission-timeline`}>
                                                                                                                                                        <td className="pr-6 pb-4">
                                                                                                                                                            {`${formatDateTime(mission?.time)} - ${mission?.commentType}`}
                                                                                                                                                        </td>
                                                                                                                                                        <td>
                                                                                                                                                            <div
                                                                                                                                                                dangerouslySetInnerHTML={{
                                                                                                                                                                    __html: mission?.description,
                                                                                                                                                                }}></div>
                                                                                                                                                        </td>
                                                                                                                                                        <td>
                                                                                                                                                            {`${mission?.author?.id > 0 ? mission?.author?.firstName + ' ' + mission?.author?.surname : ''}`}
                                                                                                                                                        </td>
                                                                                                                                                    </tr>
                                                                                                                                                ),
                                                                                                                                            )}
                                                                                                                                        </tbody>
                                                                                                                                    </table>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        )}
                                                                                                                    </>
                                                                                                                )}
                                                                                                            {event
                                                                                                                ?.eventType_Tasking
                                                                                                                ?.vesselRescueID >
                                                                                                                0 &&
                                                                                                                event
                                                                                                                    ?.eventType_Tasking
                                                                                                                    ?.type ===
                                                                                                                    'TaskingOnTow' && (
                                                                                                                    <>
                                                                                                                        {event
                                                                                                                            ?.eventType_Tasking
                                                                                                                            ?.vesselRescue
                                                                                                                            ?.mission
                                                                                                                            ?.id >
                                                                                                                            0 && (
                                                                                                                            <tr
                                                                                                                                key={`${event.eventType_Tasking.id}-et-mission`}>
                                                                                                                                <td
                                                                                                                                    className="px-6"
                                                                                                                                    colSpan={
                                                                                                                                        2
                                                                                                                                    }>
                                                                                                                                    <div className="pb-4">
                                                                                                                                        {`Towing checklist - risk analysis`}
                                                                                                                                    </div>
                                                                                                                                    <table
                                                                                                                                        className={
                                                                                                                                            tableClass
                                                                                                                                        }>
                                                                                                                                        <tbody>
                                                                                                                                            {Object.entries(
                                                                                                                                                getTowingChecklist(
                                                                                                                                                    event.eventType_Tasking,
                                                                                                                                                    trip,
                                                                                                                                                ),
                                                                                                                                            ).filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    true,
                                                                                                                                            )
                                                                                                                                                .length >
                                                                                                                                                0 && (
                                                                                                                                                <tr>
                                                                                                                                                    <td className="pr-6 pb-4 border-r-1">
                                                                                                                                                        {Object.entries(
                                                                                                                                                            getTowingChecklist(
                                                                                                                                                                event.eventType_Tasking,
                                                                                                                                                                trip,
                                                                                                                                                            ),
                                                                                                                                                        )
                                                                                                                                                            .filter(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) =>
                                                                                                                                                                    value ===
                                                                                                                                                                    true,
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) => ({
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                }),
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                (
                                                                                                                                                                    item,
                                                                                                                                                                ) => {
                                                                                                                                                                    return item.key
                                                                                                                                                                        .replace(
                                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                                            ' $1',
                                                                                                                                                                        )
                                                                                                                                                                        .replace(
                                                                                                                                                                            /^./,
                                                                                                                                                                            (
                                                                                                                                                                                str,
                                                                                                                                                                            ) =>
                                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                                        )
                                                                                                                                                                },
                                                                                                                                                            )
                                                                                                                                                            .join(
                                                                                                                                                                ', ',
                                                                                                                                                            )}
                                                                                                                                                    </td>
                                                                                                                                                    <td>
                                                                                                                                                        Ok
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                            {Object.entries(
                                                                                                                                                getTowingChecklist(
                                                                                                                                                    event.eventType_Tasking,
                                                                                                                                                    trip,
                                                                                                                                                ),
                                                                                                                                            ).filter(
                                                                                                                                                ([
                                                                                                                                                    key,
                                                                                                                                                    value,
                                                                                                                                                ]) =>
                                                                                                                                                    value ===
                                                                                                                                                    false,
                                                                                                                                            )
                                                                                                                                                .length >
                                                                                                                                                0 && (
                                                                                                                                                <tr>
                                                                                                                                                    <td>
                                                                                                                                                        {Object.entries(
                                                                                                                                                            getTowingChecklist(
                                                                                                                                                                event.eventType_Tasking,
                                                                                                                                                                trip,
                                                                                                                                                            ),
                                                                                                                                                        )
                                                                                                                                                            .filter(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) =>
                                                                                                                                                                    value ===
                                                                                                                                                                    false,
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                ([
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                ]) => ({
                                                                                                                                                                    key,
                                                                                                                                                                    value,
                                                                                                                                                                }),
                                                                                                                                                            )
                                                                                                                                                            .map(
                                                                                                                                                                (
                                                                                                                                                                    item,
                                                                                                                                                                ) => {
                                                                                                                                                                    return item.key
                                                                                                                                                                        .replace(
                                                                                                                                                                            /([A-Z])/g,
                                                                                                                                                                            ' $1',
                                                                                                                                                                        )
                                                                                                                                                                        .replace(
                                                                                                                                                                            /^./,
                                                                                                                                                                            (
                                                                                                                                                                                str,
                                                                                                                                                                            ) =>
                                                                                                                                                                                str.toUpperCase(),
                                                                                                                                                                        )
                                                                                                                                                                },
                                                                                                                                                            )
                                                                                                                                                            .join(
                                                                                                                                                                ', ',
                                                                                                                                                            )}
                                                                                                                                                    </td>
                                                                                                                                                    <td>
                                                                                                                                                        Not
                                                                                                                                                        Ok
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                            {getTowingChecklist(
                                                                                                                                                event.eventType_Tasking,
                                                                                                                                                trip,
                                                                                                                                            )
                                                                                                                                                ?.riskFactors
                                                                                                                                                ?.nodes
                                                                                                                                                ?.length >
                                                                                                                                                0 && (
                                                                                                                                                <tr>
                                                                                                                                                    <td
                                                                                                                                                        className="pr-6 pb-4"
                                                                                                                                                        colSpan={
                                                                                                                                                            2
                                                                                                                                                        }>
                                                                                                                                                        {getTowingChecklist(
                                                                                                                                                            event.eventType_Tasking,
                                                                                                                                                            trip,
                                                                                                                                                        )?.riskFactors?.nodes?.map(
                                                                                                                                                            (
                                                                                                                                                                item: any,
                                                                                                                                                                index: number,
                                                                                                                                                            ) => (
                                                                                                                                                                <div
                                                                                                                                                                    className="inline-block"
                                                                                                                                                                    key={
                                                                                                                                                                        item.id
                                                                                                                                                                    }>
                                                                                                                                                                    {index >
                                                                                                                                                                    0
                                                                                                                                                                        ? ', '
                                                                                                                                                                        : 'Risk factors: '}
                                                                                                                                                                    {`${item.title} - ${item.impact} - ${item.probability}/10`}
                                                                                                                                                                </div>
                                                                                                                                                            ),
                                                                                                                                                        )}
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                            {getTowingChecklist(
                                                                                                                                                event.eventType_Tasking,
                                                                                                                                                trip,
                                                                                                                                            )
                                                                                                                                                ?.member
                                                                                                                                                ?.id >
                                                                                                                                                0 && (
                                                                                                                                                <tr>
                                                                                                                                                    <td
                                                                                                                                                        className="pr-6 pb-4"
                                                                                                                                                        colSpan={
                                                                                                                                                            2
                                                                                                                                                        }>
                                                                                                                                                        {`Author: ${getTowingChecklist(event.eventType_Tasking, trip)?.member?.firstName} ${getTowingChecklist(event.eventType_Tasking, trip)?.member?.surname}`}
                                                                                                                                                    </td>
                                                                                                                                                </tr>
                                                                                                                                            )}
                                                                                                                                        </tbody>
                                                                                                                                    </table>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                        )}
                                                                                                                    </>
                                                                                                                )}
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </>
                                                                                    )}
                                                                                    {event.eventCategory ===
                                                                                        'RefuellingBunkering' && (
                                                                                        <>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span className="w-48 inline-block">
                                                                                                        {`Activity - Refueling and Bunkering`}
                                                                                                    </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <table
                                                                                                        className={
                                                                                                            tableClass
                                                                                                        }>
                                                                                                        <tbody>
                                                                                                            <tr
                                                                                                                key={`${event.eventType_RefuellingBunkering.id}-rb-type`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Date`}
                                                                                                                    </span>
                                                                                                                    {`${formatDateTime(event?.eventType_RefuellingBunkering?.date)}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Location`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RefuellingBunkering?.geoLocation?.id > 0 ? event?.eventType_RefuellingBunkering?.geoLocation?.title : ''}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.id}-fuel-details`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Fuel added`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RefuellingBunkering?.fuelLog?.nodes?.length ? event?.eventType_RefuellingBunkering?.fuelLog?.nodes[0]?.fuelAdded : '-'}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Fuel level`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_RefuellingBunkering?.fuelLog?.nodes?.length ? event?.eventType_RefuellingBunkering?.fuelLog?.nodes[0]?.fuelAfter : '-'}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            {event
                                                                                                                ?.eventType_RefuellingBunkering
                                                                                                                ?.notes && (
                                                                                                                <tr>
                                                                                                                    <td
                                                                                                                        colSpan={
                                                                                                                            2
                                                                                                                        }>
                                                                                                                        <div className="flex">
                                                                                                                            <span className="min-w-48 inline-block">
                                                                                                                                Notes
                                                                                                                            </span>
                                                                                                                            <div className="inline-block">
                                                                                                                                <div
                                                                                                                                    dangerouslySetInnerHTML={{
                                                                                                                                        __html: event
                                                                                                                                            ?.eventType_RefuellingBunkering
                                                                                                                                            ?.notes,
                                                                                                                                    }}></div>
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                            )}
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </>
                                                                                    )}
                                                                                    {event.eventCategory ===
                                                                                        'PassengerDropFacility' && (
                                                                                        <>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span className="w-48 inline-block">
                                                                                                        {`Activity - ${event?.eventType_PassengerDropFacility?.type.replace('Passenger', '')}`}
                                                                                                    </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <table
                                                                                                        className={
                                                                                                            tableClass
                                                                                                        }>
                                                                                                        <tbody>
                                                                                                            <tr
                                                                                                                key={`${event.eventType_PassengerDropFacility.id}-pdf-type`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Title`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_PassengerDropFacility?.title}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Time`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_PassengerDropFacility?.time}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.id}-location-fuel`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Location`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_PassengerDropFacility?.geoLocation?.id > 0 ? event?.eventType_PassengerDropFacility?.geoLocation?.title : '-'}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Fuel`}
                                                                                                                    </span>
                                                                                                                    {`${event?.eventType_PassengerDropFacility?.fuelLog?.nodes?.length ? event?.eventType_PassengerDropFacility?.fuelLog?.nodes[0]?.fuelAfter : '-'}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </>
                                                                                    )}
                                                                                    {event.eventCategory ===
                                                                                        'EventSupernumerary' && (
                                                                                        <>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span className="w-48 inline-block">
                                                                                                        {`Activity - Supernumerary`}
                                                                                                    </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <table
                                                                                                        className={
                                                                                                            tableClass
                                                                                                        }>
                                                                                                        <tbody>
                                                                                                            <tr
                                                                                                                key={`${event.supernumerary.id}-sup-type`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Title`}
                                                                                                                    </span>
                                                                                                                    {`${event?.supernumerary?.title}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Briefing time`}
                                                                                                                    </span>
                                                                                                                    {`${event?.supernumerary?.briefingTime ? event?.supernumerary?.briefingTime : '-'}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            <tr
                                                                                                                key={`${event.id}-sup-time`}>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Total guests`}
                                                                                                                    </span>
                                                                                                                    {`${event?.supernumerary?.totalGuest}`}
                                                                                                                </td>
                                                                                                                <td>
                                                                                                                    <span className="w-48 inline-block">
                                                                                                                        {`Is briefed?`}
                                                                                                                    </span>
                                                                                                                    {`${event?.supernumerary?.isBriefed ? 'Yes' : 'No'}`}
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                            {event
                                                                                                                ?.supernumerary
                                                                                                                ?.guestList
                                                                                                                ?.nodes
                                                                                                                ?.length >
                                                                                                                0 && (
                                                                                                                <tr
                                                                                                                    key={`${event.id}-guest-list`}>
                                                                                                                    <td
                                                                                                                        colSpan={
                                                                                                                            2
                                                                                                                        }>
                                                                                                                        <span className="w-48 inline-block">
                                                                                                                            {`Guests list`}
                                                                                                                        </span>
                                                                                                                        {event?.supernumerary?.guestList?.nodes?.map(
                                                                                                                            (
                                                                                                                                guest: any,
                                                                                                                            ) => (
                                                                                                                                <div
                                                                                                                                    key={
                                                                                                                                        guest.id
                                                                                                                                    }>
                                                                                                                                    {`${guest.firstName} ${guest.surname}`}
                                                                                                                                </div>
                                                                                                                            ),
                                                                                                                        )}
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                            )}
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </>
                                                                                    )}
                                                                                    {event.eventCategory ===
                                                                                        'InfringementNotice' && (
                                                                                        <>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span className="w-48 inline-block">
                                                                                                        {`Activity - InfringementNotice`}
                                                                                                    </span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <table
                                                                                                        className={
                                                                                                            tableClass
                                                                                                        }>
                                                                                                        {getInfringementsBody(
                                                                                                            event.infringementNoticeID,
                                                                                                        )}
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </>
                                                                                    )}
                                                                                </>
                                                                            ),
                                                                        )}
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </>
                                                )}
                                                <tr>
                                                    <th>Arrival</th>
                                                    <td>
                                                        {`Expected time: ${trip?.arriveTime} - Actual time: ${dayjs(trip?.arrive).format('HH:mm:ss')}`}
                                                    </td>
                                                    <th>Arrival location</th>
                                                    <td>
                                                        {trip?.toLocation?.id >
                                                        0
                                                            ? trip?.toLocation
                                                                  ?.title
                                                            : trip?.toLocation
                                                                  ?.lat +
                                                              ' ' +
                                                              trip?.toLocation
                                                                  ?.long}
                                                    </td>
                                                </tr>
                                                {trip?.comment && (
                                                    <tr>
                                                        <th>Comment</th>
                                                        <td colSpan={2}>
                                                            <div
                                                                dangerouslySetInnerHTML={{
                                                                    __html: trip?.comment,
                                                                }}></div>
                                                        </td>
                                                    </tr>
                                                )}
                                            </tbody>
                                        </table>
                                    </div>
                                ))}
                        </div>
                    </div>
                </div>
            ) : (
                <Loading message="Generating the PDF report..." />
            )}
        </>
    )
}
